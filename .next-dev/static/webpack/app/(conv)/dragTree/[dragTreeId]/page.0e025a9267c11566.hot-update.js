"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(conv)/dragTree/[dragTreeId]/page",{

/***/ "(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx":
/*!*************************************************************************!*\
  !*** ./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx ***!
  \*************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResizableLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/resizable */ \"(app-pages-browser)/./components/ui/resizable.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/hooks/useMediaQuery */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/hooks/useMediaQuery.ts\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_MobileTabNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/MobileTabNavigation */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/MobileTabNavigation.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_MobileRecommendationBanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/MobileRecommendationBanner */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/MobileRecommendationBanner.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/shared/PaneErrorBoundary */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/shared/PaneErrorBoundary.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/hooks/useAccessibility */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/hooks/useAccessibility.ts\");\n/* harmony import */ var _barrel_optimize_names_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ResizableLayout(param) {\n    let { leftPanel, rightPanel, aiPane, minLeftWidth = 20, minRightWidth = 20, minAiPaneWidth = 15, defaultLeftWidth = 50, showAiPane = false, dragTreeId = '' } = param;\n    _s();\n    const isMobile = (0,_app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__.useIsMobile)();\n    // Desktop state\n    const [isRightPanelCollapsed, setIsRightPanelCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAiPaneCollapsed, setIsAiPaneCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // ---------------------------------------------\n    // Panel flip state (outline ⇄ diagram)\n    // ---------------------------------------------\n    const LOCAL_KEY = \"dragtree_layout_flip_\".concat(dragTreeId);\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ResizableLayout.useState\": ()=>{\n            if (false) {}\n            return window.localStorage.getItem(LOCAL_KEY) === '1';\n        }\n    }[\"ResizableLayout.useState\"]);\n    const toggleFlip = ()=>{\n        setIsFlipped((prev)=>{\n            const next = !prev;\n            try {\n                window.localStorage.setItem(LOCAL_KEY, next ? '1' : '0');\n            } catch (e) {\n            // ignore\n            }\n            return next;\n        });\n    };\n    // Refs for collapsible panels\n    const rightPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aiPaneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Mobile state\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('tree');\n    // Initialize accessibility hooks\n    (0,_app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__.useKeyboardNavigation)({\n        enableKeyboardNavigation: true,\n        enableFocusManagement: true,\n        announceNavigationChanges: true\n    });\n    // Graceful error recovery function\n    const handlePaneRetry = (paneType)=>{\n        // Instead of page reload, try to reset the specific pane state\n        // This provides a better user experience\n        try {\n            // Force a re-render by updating a key prop or resetting component state\n            // For now, we'll use a targeted approach that's less disruptive than page reload\n            const event = new CustomEvent('pane-retry', {\n                detail: {\n                    paneType,\n                    timestamp: Date.now()\n                }\n            });\n            window.dispatchEvent(event);\n            // If the custom event approach doesn't work, fall back to page reload\n            // but with a small delay to allow for graceful cleanup\n            setTimeout(()=>{\n                if (confirm(\"The \".concat(paneType, \" pane encountered an error. Reload the page to recover?\"))) {\n                    window.location.reload();\n                }\n            }, 100);\n        } catch (e) {\n            // Ultimate fallback\n            window.location.reload();\n        }\n    };\n    // Handle panel collapse/expand logic for 2-panel or 3-panel layout\n    const handlePanelResize = (sizes)=>{\n        if (showAiPane && aiPane) {\n            // 3-panel layout: left, right, aiPane\n            const leftSize = sizes[0];\n            const rightSize = sizes[1] || 0;\n            // const aiPaneSize = sizes[2] || 0 // Currently unused\n            // Collapse right panel when left panel exceeds 85% (leaving room for AI pane)\n            if (leftSize > 85 && !isRightPanelCollapsed) {\n                var _rightPanelRef_current;\n                setIsRightPanelCollapsed(true);\n                (_rightPanelRef_current = rightPanelRef.current) === null || _rightPanelRef_current === void 0 ? void 0 : _rightPanelRef_current.collapse();\n            } else if (leftSize <= 85 && isRightPanelCollapsed && rightSize === 0) {\n                var _rightPanelRef_current1;\n                setIsRightPanelCollapsed(false);\n                (_rightPanelRef_current1 = rightPanelRef.current) === null || _rightPanelRef_current1 === void 0 ? void 0 : _rightPanelRef_current1.expand();\n            }\n        } else {\n            // 2-panel layout: left, right (original logic)\n            const leftSize = sizes[0];\n            const rightSize = sizes[1] || 0;\n            // Collapse right panel when left panel exceeds 90%\n            if (leftSize > 90 && !isRightPanelCollapsed) {\n                var _rightPanelRef_current2;\n                setIsRightPanelCollapsed(true);\n                (_rightPanelRef_current2 = rightPanelRef.current) === null || _rightPanelRef_current2 === void 0 ? void 0 : _rightPanelRef_current2.collapse();\n            } else if (leftSize <= 90 && isRightPanelCollapsed && rightSize === 0) {\n                var _rightPanelRef_current3;\n                setIsRightPanelCollapsed(false);\n                (_rightPanelRef_current3 = rightPanelRef.current) === null || _rightPanelRef_current3 === void 0 ? void 0 : _rightPanelRef_current3.expand();\n            }\n        }\n    };\n    const handleShowFlowView = ()=>{\n        var _rightPanelRef_current;\n        setIsRightPanelCollapsed(false);\n        (_rightPanelRef_current = rightPanelRef.current) === null || _rightPanelRef_current === void 0 ? void 0 : _rightPanelRef_current.expand();\n    };\n    const handleShowAiPane = ()=>{\n        var _aiPaneRef_current;\n        setIsAiPaneCollapsed(false);\n        (_aiPaneRef_current = aiPaneRef.current) === null || _aiPaneRef_current === void 0 ? void 0 : _aiPaneRef_current.expand();\n    };\n    // Calculate default sizes based on whether AI pane is shown\n    const getDefaultSizes = ()=>{\n        if (showAiPane && aiPane) {\n            // 3-panel layout: adjust sizes to accommodate AI pane\n            const aiPaneSize = 25 // AI pane gets 25% by default\n            ;\n            const remaining = 100 - aiPaneSize;\n            const leftSize = defaultLeftWidth / 100 * remaining;\n            const rightSize = remaining - leftSize;\n            return {\n                leftSize,\n                rightSize,\n                aiPaneSize\n            };\n        } else {\n            // 2-panel layout: original logic\n            return {\n                leftSize: defaultLeftWidth,\n                rightSize: 100 - defaultLeftWidth,\n                aiPaneSize: 0\n            };\n        }\n    };\n    const { leftSize, rightSize, aiPaneSize } = getDefaultSizes();\n    // No dynamic positioning needed - button is now fixed at leftmost position\n    // Mobile layout with tabs\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full max-w-full w-full relative overflow-hidden flex flex-col\",\n            children: [\n                dragTreeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_MobileRecommendationBanner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    dragTreeId: dragTreeId,\n                    className: \"flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_MobileTabNavigation__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    activeTab: activeTab,\n                    onTabChange: setActiveTab,\n                    showAiTab: showAiPane,\n                    className: \"flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'tree',\n                                hidden: activeTab !== 'tree'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"tree-panel\",\n                            \"aria-labelledby\": \"tree-tab\",\n                            \"aria-label\": \"Tree outline view\",\n                            \"data-pane\": \"outline\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    paneType: \"outline\",\n                                    onRetry: ()=>handlePaneRetry('outline'),\n                                    children: leftPanel\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'diagram',\n                                hidden: activeTab !== 'diagram'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"diagram-panel\",\n                            \"aria-labelledby\": \"diagram-tab\",\n                            \"aria-label\": \"Visual flow diagram view\",\n                            \"data-pane\": \"visualization\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    paneType: \"visualization\",\n                                    onRetry: ()=>handlePaneRetry('visualization'),\n                                    children: rightPanel\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        showAiPane && aiPane && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'ai',\n                                hidden: activeTab !== 'ai'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"ai-panel\",\n                            \"aria-labelledby\": \"ai-tab\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full\",\n                                children: aiPane\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    // Desktop layout with resizable panels\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"h-full max-w-full w-full relative overflow-hidden p-4 pb-2 flex flex-col min-h-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanelGroup, {\n                direction: \"horizontal\",\n                onLayout: handlePanelResize,\n                className: \"h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                        defaultSize: leftSize,\n                        minSize: minLeftWidth,\n                        maxSize: showAiPane && aiPane ? 100 - minRightWidth - minAiPaneWidth : 100 - minRightWidth,\n                        className: \"overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            role: \"region\",\n                            \"aria-label\": \"Tree outline view\",\n                            \"data-pane\": \"outline\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full', showAiPane ? 'filter blur-sm opacity-60 pointer-events-none' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                paneType: \"outline\",\n                                onRetry: ()=>handlePaneRetry('outline'),\n                                children: isFlipped ? rightPanel : leftPanel\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizableHandle, {\n                        id: \"tutorial-resize-handle\",\n                        withHandle: true,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('relative w-1 bg-gray-200 hover:bg-blue-400 transition-colors duration-200 cursor-col-resize', 'data-[resize-handle-state=hover]:bg-blue-500', 'data-[resize-handle-state=drag]:bg-blue-500'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: toggleFlip,\n                            className: \"group absolute -top-4 left-1/2 -translate-x-1/2 z-40 bg-gray-200/80 hover:bg-blue-400 border border-gray-300/60 hover:border-blue-500 shadow-sm rounded-full p-1 hover:shadow-md transition-all duration-200 ease-out\",\n                            \"aria-label\": \"Swap panes\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-3 h-3 text-gray-600 group-hover:text-white transition-colors duration-200\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute top-full left-1/2 -translate-x-1/2 mt-1.5 text-[10px] px-1.5 py-0.5 rounded bg-gray-800 text-white opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 whitespace-nowrap shadow-lg z-50\",\n                                    children: \"Swap panes\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                        ref: rightPanelRef,\n                        defaultSize: rightSize,\n                        minSize: minRightWidth,\n                        className: \"overflow-hidden\",\n                        collapsible: true,\n                        onCollapse: ()=>setIsRightPanelCollapsed(true),\n                        onExpand: ()=>setIsRightPanelCollapsed(false),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            role: \"region\",\n                            \"aria-label\": \"Visual flow diagram view\",\n                            \"data-pane\": \"visualization\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full', showAiPane ? 'filter blur-sm opacity-60 pointer-events-none' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                paneType: \"visualization\",\n                                onRetry: ()=>handlePaneRetry('visualization'),\n                                children: isFlipped ? leftPanel : rightPanel\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    showAiPane && aiPane && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizableHandle, {\n                                id: \"ai-pane-resize-handle\",\n                                withHandle: true,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('w-1 bg-gray-200 hover:bg-blue-400 transition-colors duration-200 cursor-col-resize', 'data-[resize-handle-state=hover]:bg-blue-500', 'data-[resize-handle-state=drag]:bg-blue-500')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                                ref: aiPaneRef,\n                                defaultSize: aiPaneSize,\n                                minSize: minAiPaneWidth,\n                                className: \"overflow-hidden min-w-[20rem]\",\n                                style: {\n                                    transition: 'flex-basis 0.5s ease-in-out'\n                                },\n                                collapsible: true,\n                                onCollapse: ()=>setIsAiPaneCollapsed(true),\n                                onExpand: ()=>setIsAiPaneCollapsed(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tutorial-ai-pane\",\n                                    className: \"h-full\",\n                                    children: aiPane\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            isRightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShowFlowView,\n                className: \" absolute top-4 right-4 z-10 px-3 py-1 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-200 \",\n                children: isFlipped ? 'Show Outline View' : 'Show Flow View'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, this),\n            showAiPane && aiPane && isAiPaneCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShowAiPane,\n                className: \" absolute top-12 right-4 z-10 px-3 py-1 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-200 \",\n                children: \"Show AI Assistant\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 415,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(ResizableLayout, \"zfV3G+5f1+idsHt5OP+C3pmQICY=\", false, function() {\n    return [\n        _app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__.useIsMobile,\n        _app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__.useKeyboardNavigation\n    ];\n});\n_c = ResizableLayout;\nvar _c;\n$RefreshReg$(_c, \"ResizableLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\n"));

/***/ })

});