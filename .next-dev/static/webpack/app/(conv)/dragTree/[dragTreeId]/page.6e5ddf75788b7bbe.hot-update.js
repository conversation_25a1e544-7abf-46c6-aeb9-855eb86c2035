"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(conv)/dragTree/[dragTreeId]/page",{

/***/ "(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx":
/*!*************************************************************************!*\
  !*** ./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx ***!
  \*************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResizableLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/resizable */ \"(app-pages-browser)/./components/ui/resizable.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/hooks/useMediaQuery */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/hooks/useMediaQuery.ts\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_MobileTabNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/MobileTabNavigation */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/MobileTabNavigation.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_MobileRecommendationBanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/MobileRecommendationBanner */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/MobileRecommendationBanner.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/shared/PaneErrorBoundary */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/shared/PaneErrorBoundary.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/hooks/useAccessibility */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/hooks/useAccessibility.ts\");\n/* harmony import */ var _barrel_optimize_names_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ResizableLayout(param) {\n    let { leftPanel, rightPanel, aiPane, minLeftWidth = 20, minRightWidth = 20, minAiPaneWidth = 15, defaultLeftWidth = 50, showAiPane = false, dragTreeId = '' } = param;\n    _s();\n    const isMobile = (0,_app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__.useIsMobile)();\n    // Desktop state\n    const [isRightPanelCollapsed, setIsRightPanelCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAiPaneCollapsed, setIsAiPaneCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // ---------------------------------------------\n    // Panel flip state (outline ⇄ diagram)\n    // ---------------------------------------------\n    const LOCAL_KEY = \"dragtree_layout_flip_\".concat(dragTreeId);\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ResizableLayout.useState\": ()=>{\n            if (false) {}\n            return window.localStorage.getItem(LOCAL_KEY) === '1';\n        }\n    }[\"ResizableLayout.useState\"]);\n    const toggleFlip = ()=>{\n        setIsFlipped((prev)=>{\n            const next = !prev;\n            try {\n                window.localStorage.setItem(LOCAL_KEY, next ? '1' : '0');\n            } catch (e) {\n            // ignore\n            }\n            return next;\n        });\n    };\n    // Refs for collapsible panels\n    const rightPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aiPaneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Mobile state\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('tree');\n    // Initialize accessibility hooks\n    (0,_app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__.useKeyboardNavigation)({\n        enableKeyboardNavigation: true,\n        enableFocusManagement: true,\n        announceNavigationChanges: true\n    });\n    // Graceful error recovery function\n    const handlePaneRetry = (paneType)=>{\n        // Instead of page reload, try to reset the specific pane state\n        // This provides a better user experience\n        try {\n            // Force a re-render by updating a key prop or resetting component state\n            // For now, we'll use a targeted approach that's less disruptive than page reload\n            const event = new CustomEvent('pane-retry', {\n                detail: {\n                    paneType,\n                    timestamp: Date.now()\n                }\n            });\n            window.dispatchEvent(event);\n            // If the custom event approach doesn't work, fall back to page reload\n            // but with a small delay to allow for graceful cleanup\n            setTimeout(()=>{\n                if (confirm(\"The \".concat(paneType, \" pane encountered an error. Reload the page to recover?\"))) {\n                    window.location.reload();\n                }\n            }, 100);\n        } catch (e) {\n            // Ultimate fallback\n            window.location.reload();\n        }\n    };\n    // Handle panel collapse/expand logic for 2-panel or 3-panel layout\n    const handlePanelResize = (sizes)=>{\n        if (showAiPane && aiPane) {\n            // 3-panel layout: left, right, aiPane\n            const leftSize = sizes[0];\n            const rightSize = sizes[1] || 0;\n            // const aiPaneSize = sizes[2] || 0 // Currently unused\n            // Collapse right panel when left panel exceeds 85% (leaving room for AI pane)\n            if (leftSize > 85 && !isRightPanelCollapsed) {\n                var _rightPanelRef_current;\n                setIsRightPanelCollapsed(true);\n                (_rightPanelRef_current = rightPanelRef.current) === null || _rightPanelRef_current === void 0 ? void 0 : _rightPanelRef_current.collapse();\n            } else if (leftSize <= 85 && isRightPanelCollapsed && rightSize === 0) {\n                var _rightPanelRef_current1;\n                setIsRightPanelCollapsed(false);\n                (_rightPanelRef_current1 = rightPanelRef.current) === null || _rightPanelRef_current1 === void 0 ? void 0 : _rightPanelRef_current1.expand();\n            }\n        } else {\n            // 2-panel layout: left, right (original logic)\n            const leftSize = sizes[0];\n            const rightSize = sizes[1] || 0;\n            // Collapse right panel when left panel exceeds 90%\n            if (leftSize > 90 && !isRightPanelCollapsed) {\n                var _rightPanelRef_current2;\n                setIsRightPanelCollapsed(true);\n                (_rightPanelRef_current2 = rightPanelRef.current) === null || _rightPanelRef_current2 === void 0 ? void 0 : _rightPanelRef_current2.collapse();\n            } else if (leftSize <= 90 && isRightPanelCollapsed && rightSize === 0) {\n                var _rightPanelRef_current3;\n                setIsRightPanelCollapsed(false);\n                (_rightPanelRef_current3 = rightPanelRef.current) === null || _rightPanelRef_current3 === void 0 ? void 0 : _rightPanelRef_current3.expand();\n            }\n        }\n    };\n    const handleShowFlowView = ()=>{\n        var _rightPanelRef_current;\n        setIsRightPanelCollapsed(false);\n        (_rightPanelRef_current = rightPanelRef.current) === null || _rightPanelRef_current === void 0 ? void 0 : _rightPanelRef_current.expand();\n    };\n    const handleShowAiPane = ()=>{\n        var _aiPaneRef_current;\n        setIsAiPaneCollapsed(false);\n        (_aiPaneRef_current = aiPaneRef.current) === null || _aiPaneRef_current === void 0 ? void 0 : _aiPaneRef_current.expand();\n    };\n    // Calculate default sizes based on whether AI pane is shown\n    const getDefaultSizes = ()=>{\n        if (showAiPane && aiPane) {\n            // 3-panel layout: adjust sizes to accommodate AI pane\n            const aiPaneSize = 25 // AI pane gets 25% by default\n            ;\n            const remaining = 100 - aiPaneSize;\n            const leftSize = defaultLeftWidth / 100 * remaining;\n            const rightSize = remaining - leftSize;\n            return {\n                leftSize,\n                rightSize,\n                aiPaneSize\n            };\n        } else {\n            // 2-panel layout: original logic\n            return {\n                leftSize: defaultLeftWidth,\n                rightSize: 100 - defaultLeftWidth,\n                aiPaneSize: 0\n            };\n        }\n    };\n    const { leftSize, rightSize, aiPaneSize } = getDefaultSizes();\n    // No dynamic positioning needed - button is now fixed at leftmost position\n    // Mobile layout with tabs\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full max-w-full w-full relative overflow-hidden flex flex-col\",\n            children: [\n                dragTreeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_MobileRecommendationBanner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    dragTreeId: dragTreeId,\n                    className: \"flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_MobileTabNavigation__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    activeTab: activeTab,\n                    onTabChange: setActiveTab,\n                    showAiTab: showAiPane,\n                    className: \"flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'tree',\n                                hidden: activeTab !== 'tree'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"tree-panel\",\n                            \"aria-labelledby\": \"tree-tab\",\n                            \"aria-label\": \"Tree outline view\",\n                            \"data-pane\": \"outline\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    paneType: \"outline\",\n                                    onRetry: ()=>handlePaneRetry('outline'),\n                                    children: leftPanel\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'diagram',\n                                hidden: activeTab !== 'diagram'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"diagram-panel\",\n                            \"aria-labelledby\": \"diagram-tab\",\n                            \"aria-label\": \"Visual flow diagram view\",\n                            \"data-pane\": \"visualization\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    paneType: \"visualization\",\n                                    onRetry: ()=>handlePaneRetry('visualization'),\n                                    children: rightPanel\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        showAiPane && aiPane && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'ai',\n                                hidden: activeTab !== 'ai'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"ai-panel\",\n                            \"aria-labelledby\": \"ai-tab\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full\",\n                                children: aiPane\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    // Desktop layout with resizable panels\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"h-full max-w-full w-full relative overflow-hidden p-4 pb-2 flex flex-col min-h-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleFlip,\n                className: \"group absolute top-2 left-4 z-40 bg-gray-200/80 hover:bg-blue-400 border border-gray-300/60 hover:border-blue-500 shadow-sm rounded-full p-1 hover:shadow-md transition-all duration-200 ease-out\",\n                \"aria-label\": \"Swap panes\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"w-3 h-3 text-gray-600 group-hover:text-white transition-colors duration-200\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-full left-1/2 -translate-x-1/2 mt-1.5 text-[10px] px-1.5 py-0.5 rounded bg-gray-800 text-white opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 whitespace-nowrap shadow-lg z-50\",\n                        children: \"Swap panes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanelGroup, {\n                direction: \"horizontal\",\n                onLayout: handlePanelResize,\n                className: \"h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                        defaultSize: leftSize,\n                        minSize: minLeftWidth,\n                        maxSize: showAiPane && aiPane ? 100 - minRightWidth - minAiPaneWidth : 100 - minRightWidth,\n                        className: \"overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            role: \"region\",\n                            \"aria-label\": \"Tree outline view\",\n                            \"data-pane\": \"outline\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full', showAiPane ? 'filter blur-sm opacity-60 pointer-events-none' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                paneType: \"outline\",\n                                onRetry: ()=>handlePaneRetry('outline'),\n                                children: isFlipped ? rightPanel : leftPanel\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizableHandle, {\n                        id: \"tutorial-resize-handle\",\n                        withHandle: true,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('w-1 bg-gray-200 hover:bg-blue-400 transition-colors duration-200 cursor-col-resize', 'data-[resize-handle-state=hover]:bg-blue-500', 'data-[resize-handle-state=drag]:bg-blue-500')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                        ref: rightPanelRef,\n                        defaultSize: rightSize,\n                        minSize: minRightWidth,\n                        className: \"overflow-hidden\",\n                        collapsible: true,\n                        onCollapse: ()=>setIsRightPanelCollapsed(true),\n                        onExpand: ()=>setIsRightPanelCollapsed(false),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            role: \"region\",\n                            \"aria-label\": \"Visual flow diagram view\",\n                            \"data-pane\": \"visualization\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full', showAiPane ? 'filter blur-sm opacity-60 pointer-events-none' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                paneType: \"visualization\",\n                                onRetry: ()=>handlePaneRetry('visualization'),\n                                children: isFlipped ? leftPanel : rightPanel\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    showAiPane && aiPane && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizableHandle, {\n                                id: \"ai-pane-resize-handle\",\n                                withHandle: true,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('w-1 bg-gray-200 hover:bg-blue-400 transition-colors duration-200 cursor-col-resize', 'data-[resize-handle-state=hover]:bg-blue-500', 'data-[resize-handle-state=drag]:bg-blue-500')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                                ref: aiPaneRef,\n                                defaultSize: aiPaneSize,\n                                minSize: minAiPaneWidth,\n                                className: \"overflow-hidden min-w-[20rem]\",\n                                style: {\n                                    transition: 'flex-basis 0.5s ease-in-out'\n                                },\n                                collapsible: true,\n                                onCollapse: ()=>setIsAiPaneCollapsed(true),\n                                onExpand: ()=>setIsAiPaneCollapsed(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tutorial-ai-pane\",\n                                    className: \"h-full\",\n                                    children: aiPane\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            isRightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShowFlowView,\n                className: \" absolute top-4 right-4 z-10 px-3 py-1 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-200 \",\n                children: isFlipped ? 'Show Outline View' : 'Show Flow View'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 402,\n                columnNumber: 9\n            }, this),\n            showAiPane && aiPane && isAiPaneCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShowAiPane,\n                className: \" absolute top-12 right-4 z-10 px-3 py-1 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-200 \",\n                children: \"Show AI Assistant\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(ResizableLayout, \"zfV3G+5f1+idsHt5OP+C3pmQICY=\", false, function() {\n    return [\n        _app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__.useIsMobile,\n        _app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__.useKeyboardNavigation\n    ];\n});\n_c = ResizableLayout;\nvar _c;\n$RefreshReg$(_c, \"ResizableLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\n"));

/***/ })

});