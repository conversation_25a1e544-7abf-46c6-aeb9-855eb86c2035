"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(conv)/dragTree/[dragTreeId]/page",{

/***/ "(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx":
/*!*************************************************************************!*\
  !*** ./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx ***!
  \*************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResizableLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/resizable */ \"(app-pages-browser)/./components/ui/resizable.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/hooks/useMediaQuery */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/hooks/useMediaQuery.ts\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_MobileTabNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/MobileTabNavigation */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/MobileTabNavigation.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_MobileRecommendationBanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/MobileRecommendationBanner */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/MobileRecommendationBanner.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/components/shared/PaneErrorBoundary */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/shared/PaneErrorBoundary.tsx\");\n/* harmony import */ var _app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/(conv)/dragTree/[dragTreeId]/hooks/useAccessibility */ \"(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/hooks/useAccessibility.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ResizableLayout(param) {\n    let { leftPanel, rightPanel, aiPane, minLeftWidth = 20, minRightWidth = 20, minAiPaneWidth = 15, defaultLeftWidth = 50, showAiPane = false, dragTreeId = '' } = param;\n    _s();\n    const isMobile = (0,_app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__.useIsMobile)();\n    // Desktop state\n    const [isRightPanelCollapsed, setIsRightPanelCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAiPaneCollapsed, setIsAiPaneCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // ---------------------------------------------\n    // Panel flip state (outline ⇄ diagram)\n    // ---------------------------------------------\n    const LOCAL_KEY = \"dragtree_layout_flip_\".concat(dragTreeId);\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ResizableLayout.useState\": ()=>{\n            if (false) {}\n            return window.localStorage.getItem(LOCAL_KEY) === '1';\n        }\n    }[\"ResizableLayout.useState\"]);\n    const toggleFlip = ()=>{\n        setIsFlipped((prev)=>{\n            const next = !prev;\n            try {\n                window.localStorage.setItem(LOCAL_KEY, next ? '1' : '0');\n            } catch (e) {\n            // ignore\n            }\n            return next;\n        });\n    };\n    // Refs for collapsible panels\n    const rightPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aiPaneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Mobile state\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('tree');\n    // Initialize accessibility hooks\n    (0,_app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__.useKeyboardNavigation)({\n        enableKeyboardNavigation: true,\n        enableFocusManagement: true,\n        announceNavigationChanges: true\n    });\n    // Graceful error recovery function\n    const handlePaneRetry = (paneType)=>{\n        // Instead of page reload, try to reset the specific pane state\n        // This provides a better user experience\n        try {\n            // Force a re-render by updating a key prop or resetting component state\n            // For now, we'll use a targeted approach that's less disruptive than page reload\n            const event = new CustomEvent('pane-retry', {\n                detail: {\n                    paneType,\n                    timestamp: Date.now()\n                }\n            });\n            window.dispatchEvent(event);\n            // If the custom event approach doesn't work, fall back to page reload\n            // but with a small delay to allow for graceful cleanup\n            setTimeout(()=>{\n                if (confirm(\"The \".concat(paneType, \" pane encountered an error. Reload the page to recover?\"))) {\n                    window.location.reload();\n                }\n            }, 100);\n        } catch (e) {\n            // Ultimate fallback\n            window.location.reload();\n        }\n    };\n    // Handle panel collapse/expand logic for 2-panel or 3-panel layout\n    const handlePanelResize = (sizes)=>{\n        if (showAiPane && aiPane) {\n            // 3-panel layout: left, right, aiPane\n            const leftSize = sizes[0];\n            const rightSize = sizes[1] || 0;\n            // const aiPaneSize = sizes[2] || 0 // Currently unused\n            // Collapse right panel when left panel exceeds 85% (leaving room for AI pane)\n            if (leftSize > 85 && !isRightPanelCollapsed) {\n                var _rightPanelRef_current;\n                setIsRightPanelCollapsed(true);\n                (_rightPanelRef_current = rightPanelRef.current) === null || _rightPanelRef_current === void 0 ? void 0 : _rightPanelRef_current.collapse();\n            } else if (leftSize <= 85 && isRightPanelCollapsed && rightSize === 0) {\n                var _rightPanelRef_current1;\n                setIsRightPanelCollapsed(false);\n                (_rightPanelRef_current1 = rightPanelRef.current) === null || _rightPanelRef_current1 === void 0 ? void 0 : _rightPanelRef_current1.expand();\n            }\n        } else {\n            // 2-panel layout: left, right (original logic)\n            const leftSize = sizes[0];\n            const rightSize = sizes[1] || 0;\n            // Collapse right panel when left panel exceeds 90%\n            if (leftSize > 90 && !isRightPanelCollapsed) {\n                var _rightPanelRef_current2;\n                setIsRightPanelCollapsed(true);\n                (_rightPanelRef_current2 = rightPanelRef.current) === null || _rightPanelRef_current2 === void 0 ? void 0 : _rightPanelRef_current2.collapse();\n            } else if (leftSize <= 90 && isRightPanelCollapsed && rightSize === 0) {\n                var _rightPanelRef_current3;\n                setIsRightPanelCollapsed(false);\n                (_rightPanelRef_current3 = rightPanelRef.current) === null || _rightPanelRef_current3 === void 0 ? void 0 : _rightPanelRef_current3.expand();\n            }\n        }\n    };\n    const handleShowFlowView = ()=>{\n        var _rightPanelRef_current;\n        setIsRightPanelCollapsed(false);\n        (_rightPanelRef_current = rightPanelRef.current) === null || _rightPanelRef_current === void 0 ? void 0 : _rightPanelRef_current.expand();\n    };\n    const handleShowAiPane = ()=>{\n        var _aiPaneRef_current;\n        setIsAiPaneCollapsed(false);\n        (_aiPaneRef_current = aiPaneRef.current) === null || _aiPaneRef_current === void 0 ? void 0 : _aiPaneRef_current.expand();\n    };\n    // Calculate default sizes based on whether AI pane is shown\n    const getDefaultSizes = ()=>{\n        if (showAiPane && aiPane) {\n            // 3-panel layout: adjust sizes to accommodate AI pane\n            const aiPaneSize = 25 // AI pane gets 25% by default\n            ;\n            const remaining = 100 - aiPaneSize;\n            const leftSize = defaultLeftWidth / 100 * remaining;\n            const rightSize = remaining - leftSize;\n            return {\n                leftSize,\n                rightSize,\n                aiPaneSize\n            };\n        } else {\n            // 2-panel layout: original logic\n            return {\n                leftSize: defaultLeftWidth,\n                rightSize: 100 - defaultLeftWidth,\n                aiPaneSize: 0\n            };\n        }\n    };\n    const { leftSize, rightSize, aiPaneSize } = getDefaultSizes();\n    // No dynamic positioning needed - button is now fixed at leftmost position\n    // Mobile layout with tabs\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full max-w-full w-full relative overflow-hidden flex flex-col\",\n            children: [\n                dragTreeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_MobileRecommendationBanner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    dragTreeId: dragTreeId,\n                    className: \"flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_MobileTabNavigation__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    activeTab: activeTab,\n                    onTabChange: setActiveTab,\n                    showAiTab: showAiPane,\n                    className: \"flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'tree',\n                                hidden: activeTab !== 'tree'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"tree-panel\",\n                            \"aria-labelledby\": \"tree-tab\",\n                            \"aria-label\": \"Tree outline view\",\n                            \"data-pane\": \"outline\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    paneType: \"outline\",\n                                    onRetry: ()=>handlePaneRetry('outline'),\n                                    children: leftPanel\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'diagram',\n                                hidden: activeTab !== 'diagram'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"diagram-panel\",\n                            \"aria-labelledby\": \"diagram-tab\",\n                            \"aria-label\": \"Visual flow diagram view\",\n                            \"data-pane\": \"visualization\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    paneType: \"visualization\",\n                                    onRetry: ()=>handlePaneRetry('visualization'),\n                                    children: rightPanel\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        showAiPane && aiPane && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full overflow-hidden transition-all duration-300', {\n                                block: activeTab === 'ai',\n                                hidden: activeTab !== 'ai'\n                            }),\n                            role: \"tabpanel\",\n                            id: \"ai-panel\",\n                            \"aria-labelledby\": \"ai-tab\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full\",\n                                children: aiPane\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    // Desktop layout with resizable panels\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"h-full max-w-full w-full relative overflow-hidden p-4 pb-2 flex flex-col min-h-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanelGroup, {\n                direction: \"horizontal\",\n                onLayout: handlePanelResize,\n                className: \"h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                        defaultSize: leftSize,\n                        minSize: minLeftWidth,\n                        maxSize: showAiPane && aiPane ? 100 - minRightWidth - minAiPaneWidth : 100 - minRightWidth,\n                        className: \"overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            role: \"region\",\n                            \"aria-label\": \"Tree outline view\",\n                            \"data-pane\": \"outline\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full', showAiPane ? 'filter blur-sm opacity-60 pointer-events-none' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                paneType: \"outline\",\n                                onRetry: ()=>handlePaneRetry('outline'),\n                                children: isFlipped ? rightPanel : leftPanel\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizableHandle, {\n                        id: \"tutorial-resize-handle\",\n                        withHandle: true,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('w-1 bg-gray-200 hover:bg-blue-400 transition-colors duration-200 cursor-col-resize', 'data-[resize-handle-state=hover]:bg-blue-500', 'data-[resize-handle-state=drag]:bg-blue-500')\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                        ref: rightPanelRef,\n                        defaultSize: rightSize,\n                        minSize: minRightWidth,\n                        className: \"overflow-hidden\",\n                        collapsible: true,\n                        onCollapse: ()=>setIsRightPanelCollapsed(true),\n                        onExpand: ()=>setIsRightPanelCollapsed(false),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            role: \"region\",\n                            \"aria-label\": \"Visual flow diagram view\",\n                            \"data-pane\": \"visualization\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-full', showAiPane ? 'filter blur-sm opacity-60 pointer-events-none' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_conv_dragTree_dragTreeId_components_shared_PaneErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                paneType: \"visualization\",\n                                onRetry: ()=>handlePaneRetry('visualization'),\n                                children: isFlipped ? leftPanel : rightPanel\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    showAiPane && aiPane && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizableHandle, {\n                                id: \"ai-pane-resize-handle\",\n                                withHandle: true,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('w-1 bg-gray-200 hover:bg-blue-400 transition-colors duration-200 cursor-col-resize', 'data-[resize-handle-state=hover]:bg-blue-500', 'data-[resize-handle-state=drag]:bg-blue-500')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_3__.ResizablePanel, {\n                                ref: aiPaneRef,\n                                defaultSize: aiPaneSize,\n                                minSize: minAiPaneWidth,\n                                className: \"overflow-hidden min-w-[20rem]\",\n                                style: {\n                                    transition: 'flex-basis 0.5s ease-in-out'\n                                },\n                                collapsible: true,\n                                onCollapse: ()=>setIsAiPaneCollapsed(true),\n                                onExpand: ()=>setIsAiPaneCollapsed(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tutorial-ai-pane\",\n                                    className: \"h-full\",\n                                    children: aiPane\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            isRightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShowFlowView,\n                className: \" absolute top-4 right-4 z-10 px-3 py-1 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-200 \",\n                children: isFlipped ? 'Show Outline View' : 'Show Flow View'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 390,\n                columnNumber: 9\n            }, this),\n            showAiPane && aiPane && isAiPaneCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleShowAiPane,\n                className: \" absolute top-12 right-4 z-10 px-3 py-1 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-200 \",\n                children: \"Show AI Assistant\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n                lineNumber: 402,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(ResizableLayout, \"zfV3G+5f1+idsHt5OP+C3pmQICY=\", false, function() {\n    return [\n        _app_conv_dragTree_dragTreeId_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_4__.useIsMobile,\n        _app_conv_dragTree_dragTreeId_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__.useKeyboardNavigation\n    ];\n});\n_c = ResizableLayout;\nvar _c;\n$RefreshReg$(_c, \"ResizableLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC8oY29udikvZHJhZ1RyZWUvW2RyYWdUcmVlSWRdL2NvbXBvbmVudHMvUmVzaXphYmxlTGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRStDO0FBQ2Y7QUFLRTtBQUVrRDtBQUdWO0FBQ3VDO0FBQ1g7QUFDTDtBQWVsRixTQUFTWSxnQkFBZ0IsS0FVakI7UUFWaUIsRUFDdENDLFNBQVMsRUFDVEMsVUFBVSxFQUNWQyxNQUFNLEVBQ05DLGVBQWUsRUFBRSxFQUNqQkMsZ0JBQWdCLEVBQUUsRUFDbEJDLGlCQUFpQixFQUFFLEVBQ25CQyxtQkFBbUIsRUFBRSxFQUNyQkMsYUFBYSxLQUFLLEVBQ2xCQyxhQUFhLEVBQUUsRUFDTSxHQVZpQjs7SUFXdEMsTUFBTUMsV0FBV2YsOEZBQVdBO0lBRTVCLGdCQUFnQjtJQUNoQixNQUFNLENBQUNnQix1QkFBdUJDLHlCQUF5QixHQUNyRHZCLCtDQUFRQSxDQUFVO0lBQ3BCLE1BQU0sQ0FBQ3dCLG1CQUFtQkMscUJBQXFCLEdBQUd6QiwrQ0FBUUEsQ0FBVTtJQUVwRSxNQUFNMEIsZUFBZXpCLDZDQUFNQSxDQUFpQjtJQUU1QyxnREFBZ0Q7SUFDaEQsdUNBQXVDO0lBQ3ZDLGdEQUFnRDtJQUNoRCxNQUFNMEIsWUFBWSx3QkFBbUMsT0FBWFA7SUFDMUMsTUFBTSxDQUFDUSxXQUFXQyxhQUFhLEdBQUc3QiwrQ0FBUUE7b0NBQVU7WUFDbEQsSUFBSSxLQUE2QixFQUFFLEVBQU87WUFDMUMsT0FBTzhCLE9BQU9DLFlBQVksQ0FBQ0MsT0FBTyxDQUFDTCxlQUFlO1FBQ3BEOztJQUVBLE1BQU1NLGFBQWE7UUFDakJKLGFBQWFLLENBQUFBO1lBQ1gsTUFBTUMsT0FBTyxDQUFDRDtZQUNkLElBQUk7Z0JBQ0ZKLE9BQU9DLFlBQVksQ0FBQ0ssT0FBTyxDQUFDVCxXQUFXUSxPQUFPLE1BQU07WUFDdEQsRUFBRSxVQUFNO1lBQ04sU0FBUztZQUNYO1lBQ0EsT0FBT0E7UUFDVDtJQUNGO0lBRUEsOEJBQThCO0lBQzlCLE1BQU1FLGdCQUFnQnBDLDZDQUFNQSxDQUF3QjtJQUNwRCxNQUFNcUMsWUFBWXJDLDZDQUFNQSxDQUF3QjtJQUVoRCxlQUFlO0lBQ2YsTUFBTSxDQUFDc0MsV0FBV0MsYUFBYSxHQUFHeEMsK0NBQVFBLENBQVk7SUFFdEQsaUNBQWlDO0lBQ2pDVSwyR0FBcUJBLENBQUM7UUFDcEIrQiwwQkFBMEI7UUFDMUJDLHVCQUF1QjtRQUN2QkMsMkJBQTJCO0lBQzdCO0lBRUEsbUNBQW1DO0lBQ25DLE1BQU1DLGtCQUFrQixDQUFDQztRQUN2QiwrREFBK0Q7UUFDL0QseUNBQXlDO1FBQ3pDLElBQUk7WUFDRix3RUFBd0U7WUFDeEUsaUZBQWlGO1lBQ2pGLE1BQU1DLFFBQVEsSUFBSUMsWUFBWSxjQUFjO2dCQUMxQ0MsUUFBUTtvQkFBRUg7b0JBQVVJLFdBQVdDLEtBQUtDLEdBQUc7Z0JBQUc7WUFDNUM7WUFDQXJCLE9BQU9zQixhQUFhLENBQUNOO1lBRXJCLHNFQUFzRTtZQUN0RSx1REFBdUQ7WUFDdkRPLFdBQVc7Z0JBQ1QsSUFDRUMsUUFDRSxPQUFnQixPQUFUVCxVQUFTLDZEQUVsQjtvQkFDQWYsT0FBT3lCLFFBQVEsQ0FBQ0MsTUFBTTtnQkFDeEI7WUFDRixHQUFHO1FBQ0wsRUFBRSxVQUFNO1lBQ04sb0JBQW9CO1lBQ3BCMUIsT0FBT3lCLFFBQVEsQ0FBQ0MsTUFBTTtRQUN4QjtJQUNGO0lBRUEsbUVBQW1FO0lBQ25FLE1BQU1DLG9CQUFvQixDQUFDQztRQUN6QixJQUFJdkMsY0FBY0wsUUFBUTtZQUN4QixzQ0FBc0M7WUFDdEMsTUFBTTZDLFdBQVdELEtBQUssQ0FBQyxFQUFFO1lBQ3pCLE1BQU1FLFlBQVlGLEtBQUssQ0FBQyxFQUFFLElBQUk7WUFDOUIsdURBQXVEO1lBRXZELDhFQUE4RTtZQUM5RSxJQUFJQyxXQUFXLE1BQU0sQ0FBQ3JDLHVCQUF1QjtvQkFFM0NlO2dCQURBZCx5QkFBeUI7aUJBQ3pCYyx5QkFBQUEsY0FBY3dCLE9BQU8sY0FBckJ4Qiw2Q0FBQUEsdUJBQXVCeUIsUUFBUTtZQUNqQyxPQUFPLElBQUlILFlBQVksTUFBTXJDLHlCQUF5QnNDLGNBQWMsR0FBRztvQkFFckV2QjtnQkFEQWQseUJBQXlCO2lCQUN6QmMsMEJBQUFBLGNBQWN3QixPQUFPLGNBQXJCeEIsOENBQUFBLHdCQUF1QjBCLE1BQU07WUFDL0I7UUFDRixPQUFPO1lBQ0wsK0NBQStDO1lBQy9DLE1BQU1KLFdBQVdELEtBQUssQ0FBQyxFQUFFO1lBQ3pCLE1BQU1FLFlBQVlGLEtBQUssQ0FBQyxFQUFFLElBQUk7WUFFOUIsbURBQW1EO1lBQ25ELElBQUlDLFdBQVcsTUFBTSxDQUFDckMsdUJBQXVCO29CQUUzQ2U7Z0JBREFkLHlCQUF5QjtpQkFDekJjLDBCQUFBQSxjQUFjd0IsT0FBTyxjQUFyQnhCLDhDQUFBQSx3QkFBdUJ5QixRQUFRO1lBQ2pDLE9BQU8sSUFBSUgsWUFBWSxNQUFNckMseUJBQXlCc0MsY0FBYyxHQUFHO29CQUVyRXZCO2dCQURBZCx5QkFBeUI7aUJBQ3pCYywwQkFBQUEsY0FBY3dCLE9BQU8sY0FBckJ4Qiw4Q0FBQUEsd0JBQXVCMEIsTUFBTTtZQUMvQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxxQkFBcUI7WUFFekIzQjtRQURBZCx5QkFBeUI7U0FDekJjLHlCQUFBQSxjQUFjd0IsT0FBTyxjQUFyQnhCLDZDQUFBQSx1QkFBdUIwQixNQUFNO0lBQy9CO0lBRUEsTUFBTUUsbUJBQW1CO1lBRXZCM0I7UUFEQWIscUJBQXFCO1NBQ3JCYSxxQkFBQUEsVUFBVXVCLE9BQU8sY0FBakJ2Qix5Q0FBQUEsbUJBQW1CeUIsTUFBTTtJQUMzQjtJQUVBLDREQUE0RDtJQUM1RCxNQUFNRyxrQkFBa0I7UUFDdEIsSUFBSS9DLGNBQWNMLFFBQVE7WUFDeEIsc0RBQXNEO1lBQ3RELE1BQU1xRCxhQUFhLEdBQUcsOEJBQThCOztZQUNwRCxNQUFNQyxZQUFZLE1BQU1EO1lBQ3hCLE1BQU1SLFdBQVcsbUJBQW9CLE1BQU9TO1lBQzVDLE1BQU1SLFlBQVlRLFlBQVlUO1lBQzlCLE9BQU87Z0JBQUVBO2dCQUFVQztnQkFBV087WUFBVztRQUMzQyxPQUFPO1lBQ0wsaUNBQWlDO1lBQ2pDLE9BQU87Z0JBQ0xSLFVBQVV6QztnQkFDVjBDLFdBQVcsTUFBTTFDO2dCQUNqQmlELFlBQVk7WUFDZDtRQUNGO0lBQ0Y7SUFFQSxNQUFNLEVBQUVSLFFBQVEsRUFBRUMsU0FBUyxFQUFFTyxVQUFVLEVBQUUsR0FBR0Q7SUFFNUMsMkVBQTJFO0lBRTNFLDBCQUEwQjtJQUMxQixJQUFJN0MsVUFBVTtRQUNaLHFCQUNFLDhEQUFDZ0Q7WUFBSUMsV0FBVTs7Z0JBRVpsRCw0QkFDQyw4REFBQ1osMkdBQTBCQTtvQkFDekJZLFlBQVlBO29CQUNaa0QsV0FBVTs7Ozs7OzhCQUtkLDhEQUFDL0Qsb0dBQW1CQTtvQkFDbEJnQyxXQUFXQTtvQkFDWGdDLGFBQWEvQjtvQkFDYmdDLFdBQVdyRDtvQkFDWG1ELFdBQVU7Ozs7Ozs4QkFJWiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFDQ0MsV0FBV3BFLDhDQUFFQSxDQUNYLHNEQUNBO2dDQUNFdUUsT0FBT2xDLGNBQWM7Z0NBQ3JCbUMsUUFBUW5DLGNBQWM7NEJBQ3hCOzRCQUVGb0MsTUFBSzs0QkFDTEMsSUFBRzs0QkFDSEMsbUJBQWdCOzRCQUNoQkMsY0FBVzs0QkFDWEMsYUFBVTtzQ0FFViw0RUFBQ1Y7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUM3RCx5R0FBaUJBO29DQUNoQm9DLFVBQVM7b0NBQ1RtQyxTQUFTLElBQU1wQyxnQkFBZ0I7OENBRTlCaEM7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTVAsOERBQUN5RDs0QkFDQ0MsV0FBV3BFLDhDQUFFQSxDQUNYLHNEQUNBO2dDQUNFdUUsT0FBT2xDLGNBQWM7Z0NBQ3JCbUMsUUFBUW5DLGNBQWM7NEJBQ3hCOzRCQUVGb0MsTUFBSzs0QkFDTEMsSUFBRzs0QkFDSEMsbUJBQWdCOzRCQUNoQkMsY0FBVzs0QkFDWEMsYUFBVTtzQ0FFViw0RUFBQ1Y7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUM3RCx5R0FBaUJBO29DQUNoQm9DLFVBQVM7b0NBQ1RtQyxTQUFTLElBQU1wQyxnQkFBZ0I7OENBRTlCL0I7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBTU5NLGNBQWNMLHdCQUNiLDhEQUFDdUQ7NEJBQ0NDLFdBQVdwRSw4Q0FBRUEsQ0FDWCxzREFDQTtnQ0FDRXVFLE9BQU9sQyxjQUFjO2dDQUNyQm1DLFFBQVFuQyxjQUFjOzRCQUN4Qjs0QkFFRm9DLE1BQUs7NEJBQ0xDLElBQUc7NEJBQ0hDLG1CQUFnQjtzQ0FFaEIsNEVBQUNSO2dDQUFJQyxXQUFVOzBDQUFVeEQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTXJDO0lBRUEsdUNBQXVDO0lBQ3ZDLHFCQUNFLDhEQUFDdUQ7UUFDQ1ksS0FBS3ZEO1FBQ0w0QyxXQUFVOzswQkFHViw4REFBQ25FLHlFQUFtQkE7Z0JBQ2xCK0UsV0FBVTtnQkFDVkMsVUFBVTFCO2dCQUNWYSxXQUFVOztrQ0FHViw4REFBQ2xFLG9FQUFjQTt3QkFDYmdGLGFBQWF6Qjt3QkFDYjBCLFNBQVN0RTt3QkFDVHVFLFNBQ0VuRSxjQUFjTCxTQUNWLE1BQU1FLGdCQUFnQkMsaUJBQ3RCLE1BQU1EO3dCQUVac0QsV0FBVTtrQ0FFViw0RUFBQ0Q7NEJBQ0NNLE1BQUs7NEJBQ0xHLGNBQVc7NEJBQ1hDLGFBQVU7NEJBQ1ZULFdBQVdwRSw4Q0FBRUEsQ0FDWCxVQUNBaUIsYUFBYSxrREFBa0Q7c0NBR2pFLDRFQUFDVix5R0FBaUJBO2dDQUNoQm9DLFVBQVM7Z0NBQ1RtQyxTQUFTLElBQU1wQyxnQkFBZ0I7MENBRTlCaEIsWUFBWWYsYUFBYUQ7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTWhDLDhEQUFDUCxxRUFBZUE7d0JBQ2R1RSxJQUFHO3dCQUNIVyxVQUFVO3dCQUNWakIsV0FBV3BFLDhDQUFFQSxDQUNYLHNGQUNBLGdEQUNBOzs7Ozs7a0NBS0osOERBQUNFLG9FQUFjQTt3QkFDYjZFLEtBQUs1Qzt3QkFDTCtDLGFBQWF4Qjt3QkFDYnlCLFNBQVNyRTt3QkFDVHNELFdBQVU7d0JBQ1ZrQixhQUFhO3dCQUNiQyxZQUFZLElBQU1sRSx5QkFBeUI7d0JBQzNDbUUsVUFBVSxJQUFNbkUseUJBQXlCO2tDQUV6Qyw0RUFBQzhDOzRCQUNDTSxNQUFLOzRCQUNMRyxjQUFXOzRCQUNYQyxhQUFVOzRCQUNWVCxXQUFXcEUsOENBQUVBLENBQ1gsVUFDQWlCLGFBQWEsa0RBQWtEO3NDQUdqRSw0RUFBQ1YseUdBQWlCQTtnQ0FDaEJvQyxVQUFTO2dDQUNUbUMsU0FBUyxJQUFNcEMsZ0JBQWdCOzBDQUU5QmhCLFlBQVloQixZQUFZQzs7Ozs7Ozs7Ozs7Ozs7OztvQkFNOUJNLGNBQWNMLHdCQUNiOzswQ0FFRSw4REFBQ1QscUVBQWVBO2dDQUNkdUUsSUFBRztnQ0FDSFcsVUFBVTtnQ0FDVmpCLFdBQVdwRSw4Q0FBRUEsQ0FDWCxzRkFDQSxnREFDQTs7Ozs7OzBDQUtKLDhEQUFDRSxvRUFBY0E7Z0NBQ2I2RSxLQUFLM0M7Z0NBQ0w4QyxhQUFhakI7Z0NBQ2JrQixTQUFTcEU7Z0NBQ1RxRCxXQUFVO2dDQUNWcUIsT0FBTztvQ0FBRUMsWUFBWTtnQ0FBOEI7Z0NBQ25ESixhQUFhO2dDQUNiQyxZQUFZLElBQU1oRSxxQkFBcUI7Z0NBQ3ZDaUUsVUFBVSxJQUFNakUscUJBQXFCOzBDQUVyQyw0RUFBQzRDO29DQUFJTyxJQUFHO29DQUFtQk4sV0FBVTs4Q0FDbEN4RDs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVFWUSx1Q0FDQyw4REFBQ3VFO2dCQUNDQyxTQUFTOUI7Z0JBQ1RNLFdBQVU7MEJBS1QxQyxZQUFZLHNCQUFzQjs7Ozs7O1lBSXRDVCxjQUFjTCxVQUFVVSxtQ0FDdkIsOERBQUNxRTtnQkFDQ0MsU0FBUzdCO2dCQUNUSyxXQUFVOzBCQUlYOzs7Ozs7Ozs7Ozs7QUFNVDtHQTlYd0IzRDs7UUFXTEwsMEZBQVdBO1FBc0M1QkksdUdBQXFCQTs7O0tBakRDQyIsInNvdXJjZXMiOlsiL1VzZXJzL2dlb2ZmcmV5aHVuZy9EZXNrdG9wL2dpdGh1Yi9uZXh0MTMtY2xhcmlmeS9hcHAvKGNvbnYpL2RyYWdUcmVlL1tkcmFnVHJlZUlkXS9jb21wb25lbnRzL1Jlc2l6YWJsZUxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuaW1wb3J0IHtcbiAgUmVzaXphYmxlUGFuZWxHcm91cCxcbiAgUmVzaXphYmxlUGFuZWwsXG4gIFJlc2l6YWJsZUhhbmRsZSxcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3Jlc2l6YWJsZSdcbmltcG9ydCB0eXBlIHsgSW1wZXJhdGl2ZVBhbmVsSGFuZGxlIH0gZnJvbSAncmVhY3QtcmVzaXphYmxlLXBhbmVscydcbmltcG9ydCB7IHVzZUlzTW9iaWxlIH0gZnJvbSAnQC9hcHAvKGNvbnYpL2RyYWdUcmVlL1tkcmFnVHJlZUlkXS9ob29rcy91c2VNZWRpYVF1ZXJ5J1xuaW1wb3J0IE1vYmlsZVRhYk5hdmlnYXRpb24sIHtcbiAgdHlwZSBNb2JpbGVUYWIsXG59IGZyb20gJ0AvYXBwLyhjb252KS9kcmFnVHJlZS9bZHJhZ1RyZWVJZF0vY29tcG9uZW50cy9Nb2JpbGVUYWJOYXZpZ2F0aW9uJ1xuaW1wb3J0IE1vYmlsZVJlY29tbWVuZGF0aW9uQmFubmVyIGZyb20gJ0AvYXBwLyhjb252KS9kcmFnVHJlZS9bZHJhZ1RyZWVJZF0vY29tcG9uZW50cy9Nb2JpbGVSZWNvbW1lbmRhdGlvbkJhbm5lcidcbmltcG9ydCBQYW5lRXJyb3JCb3VuZGFyeSBmcm9tICdAL2FwcC8oY29udikvZHJhZ1RyZWUvW2RyYWdUcmVlSWRdL2NvbXBvbmVudHMvc2hhcmVkL1BhbmVFcnJvckJvdW5kYXJ5J1xuaW1wb3J0IHsgdXNlS2V5Ym9hcmROYXZpZ2F0aW9uIH0gZnJvbSAnQC9hcHAvKGNvbnYpL2RyYWdUcmVlL1tkcmFnVHJlZUlkXS9ob29rcy91c2VBY2Nlc3NpYmlsaXR5J1xuaW1wb3J0IHsgUm90YXRlQ2N3IH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG50eXBlIFJlc2l6YWJsZUxheW91dFByb3BzID0ge1xuICBsZWZ0UGFuZWw6IFJlYWN0LlJlYWN0Tm9kZVxuICByaWdodFBhbmVsOiBSZWFjdC5SZWFjdE5vZGVcbiAgYWlQYW5lPzogUmVhY3QuUmVhY3ROb2RlXG4gIG1pbkxlZnRXaWR0aD86IG51bWJlclxuICBtaW5SaWdodFdpZHRoPzogbnVtYmVyXG4gIG1pbkFpUGFuZVdpZHRoPzogbnVtYmVyXG4gIGRlZmF1bHRMZWZ0V2lkdGg/OiBudW1iZXJcbiAgc2hvd0FpUGFuZT86IGJvb2xlYW5cbiAgZHJhZ1RyZWVJZD86IHN0cmluZyAvLyBGb3IgYmFubmVyIHRyYWNraW5nIHBlciBkcmFnIHRyZWVcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUmVzaXphYmxlTGF5b3V0KHtcbiAgbGVmdFBhbmVsLFxuICByaWdodFBhbmVsLFxuICBhaVBhbmUsXG4gIG1pbkxlZnRXaWR0aCA9IDIwLCAvLyBwZXJjZW50YWdlIC0gbWluaW11bSBsZWZ0IHBhbmVsIHNpemVcbiAgbWluUmlnaHRXaWR0aCA9IDIwLCAvLyBwZXJjZW50YWdlIC0gbWluaW11bSByaWdodCBwYW5lbCBzaXplXG4gIG1pbkFpUGFuZVdpZHRoID0gMTUsIC8vIHBlcmNlbnRhZ2UgLSBtaW5pbXVtIEFJIHBhbmUgc2l6ZVxuICBkZWZhdWx0TGVmdFdpZHRoID0gNTAsIC8vIHBlcmNlbnRhZ2UgLSBlcXVhbCBzcGxpdCBieSBkZWZhdWx0XG4gIHNob3dBaVBhbmUgPSBmYWxzZSxcbiAgZHJhZ1RyZWVJZCA9ICcnLFxufTogUmVzaXphYmxlTGF5b3V0UHJvcHMpIHtcbiAgY29uc3QgaXNNb2JpbGUgPSB1c2VJc01vYmlsZSgpXG5cbiAgLy8gRGVza3RvcCBzdGF0ZVxuICBjb25zdCBbaXNSaWdodFBhbmVsQ29sbGFwc2VkLCBzZXRJc1JpZ2h0UGFuZWxDb2xsYXBzZWRdID1cbiAgICB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSlcbiAgY29uc3QgW2lzQWlQYW5lQ29sbGFwc2VkLCBzZXRJc0FpUGFuZUNvbGxhcHNlZF0gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSlcblxuICBjb25zdCBjb250YWluZXJSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpXG5cbiAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gIC8vIFBhbmVsIGZsaXAgc3RhdGUgKG91dGxpbmUg4oeEIGRpYWdyYW0pXG4gIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICBjb25zdCBMT0NBTF9LRVkgPSBgZHJhZ3RyZWVfbGF5b3V0X2ZsaXBfJHtkcmFnVHJlZUlkfWBcbiAgY29uc3QgW2lzRmxpcHBlZCwgc2V0SXNGbGlwcGVkXSA9IHVzZVN0YXRlPGJvb2xlYW4+KCgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBmYWxzZVxuICAgIHJldHVybiB3aW5kb3cubG9jYWxTdG9yYWdlLmdldEl0ZW0oTE9DQUxfS0VZKSA9PT0gJzEnXG4gIH0pXG5cbiAgY29uc3QgdG9nZ2xlRmxpcCA9ICgpID0+IHtcbiAgICBzZXRJc0ZsaXBwZWQocHJldiA9PiB7XG4gICAgICBjb25zdCBuZXh0ID0gIXByZXZcbiAgICAgIHRyeSB7XG4gICAgICAgIHdpbmRvdy5sb2NhbFN0b3JhZ2Uuc2V0SXRlbShMT0NBTF9LRVksIG5leHQgPyAnMScgOiAnMCcpXG4gICAgICB9IGNhdGNoIHtcbiAgICAgICAgLy8gaWdub3JlXG4gICAgICB9XG4gICAgICByZXR1cm4gbmV4dFxuICAgIH0pXG4gIH1cblxuICAvLyBSZWZzIGZvciBjb2xsYXBzaWJsZSBwYW5lbHNcbiAgY29uc3QgcmlnaHRQYW5lbFJlZiA9IHVzZVJlZjxJbXBlcmF0aXZlUGFuZWxIYW5kbGU+KG51bGwpXG4gIGNvbnN0IGFpUGFuZVJlZiA9IHVzZVJlZjxJbXBlcmF0aXZlUGFuZWxIYW5kbGU+KG51bGwpXG5cbiAgLy8gTW9iaWxlIHN0YXRlXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTxNb2JpbGVUYWI+KCd0cmVlJylcblxuICAvLyBJbml0aWFsaXplIGFjY2Vzc2liaWxpdHkgaG9va3NcbiAgdXNlS2V5Ym9hcmROYXZpZ2F0aW9uKHtcbiAgICBlbmFibGVLZXlib2FyZE5hdmlnYXRpb246IHRydWUsXG4gICAgZW5hYmxlRm9jdXNNYW5hZ2VtZW50OiB0cnVlLFxuICAgIGFubm91bmNlTmF2aWdhdGlvbkNoYW5nZXM6IHRydWUsXG4gIH0pXG5cbiAgLy8gR3JhY2VmdWwgZXJyb3IgcmVjb3ZlcnkgZnVuY3Rpb25cbiAgY29uc3QgaGFuZGxlUGFuZVJldHJ5ID0gKHBhbmVUeXBlOiAnb3V0bGluZScgfCAndmlzdWFsaXphdGlvbicpID0+IHtcbiAgICAvLyBJbnN0ZWFkIG9mIHBhZ2UgcmVsb2FkLCB0cnkgdG8gcmVzZXQgdGhlIHNwZWNpZmljIHBhbmUgc3RhdGVcbiAgICAvLyBUaGlzIHByb3ZpZGVzIGEgYmV0dGVyIHVzZXIgZXhwZXJpZW5jZVxuICAgIHRyeSB7XG4gICAgICAvLyBGb3JjZSBhIHJlLXJlbmRlciBieSB1cGRhdGluZyBhIGtleSBwcm9wIG9yIHJlc2V0dGluZyBjb21wb25lbnQgc3RhdGVcbiAgICAgIC8vIEZvciBub3csIHdlJ2xsIHVzZSBhIHRhcmdldGVkIGFwcHJvYWNoIHRoYXQncyBsZXNzIGRpc3J1cHRpdmUgdGhhbiBwYWdlIHJlbG9hZFxuICAgICAgY29uc3QgZXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoJ3BhbmUtcmV0cnknLCB7XG4gICAgICAgIGRldGFpbDogeyBwYW5lVHlwZSwgdGltZXN0YW1wOiBEYXRlLm5vdygpIH0sXG4gICAgICB9KVxuICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQoZXZlbnQpXG5cbiAgICAgIC8vIElmIHRoZSBjdXN0b20gZXZlbnQgYXBwcm9hY2ggZG9lc24ndCB3b3JrLCBmYWxsIGJhY2sgdG8gcGFnZSByZWxvYWRcbiAgICAgIC8vIGJ1dCB3aXRoIGEgc21hbGwgZGVsYXkgdG8gYWxsb3cgZm9yIGdyYWNlZnVsIGNsZWFudXBcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBpZiAoXG4gICAgICAgICAgY29uZmlybShcbiAgICAgICAgICAgIGBUaGUgJHtwYW5lVHlwZX0gcGFuZSBlbmNvdW50ZXJlZCBhbiBlcnJvci4gUmVsb2FkIHRoZSBwYWdlIHRvIHJlY292ZXI/YFxuICAgICAgICAgIClcbiAgICAgICAgKSB7XG4gICAgICAgICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpXG4gICAgICAgIH1cbiAgICAgIH0sIDEwMClcbiAgICB9IGNhdGNoIHtcbiAgICAgIC8vIFVsdGltYXRlIGZhbGxiYWNrXG4gICAgICB3aW5kb3cubG9jYXRpb24ucmVsb2FkKClcbiAgICB9XG4gIH1cblxuICAvLyBIYW5kbGUgcGFuZWwgY29sbGFwc2UvZXhwYW5kIGxvZ2ljIGZvciAyLXBhbmVsIG9yIDMtcGFuZWwgbGF5b3V0XG4gIGNvbnN0IGhhbmRsZVBhbmVsUmVzaXplID0gKHNpemVzOiBudW1iZXJbXSkgPT4ge1xuICAgIGlmIChzaG93QWlQYW5lICYmIGFpUGFuZSkge1xuICAgICAgLy8gMy1wYW5lbCBsYXlvdXQ6IGxlZnQsIHJpZ2h0LCBhaVBhbmVcbiAgICAgIGNvbnN0IGxlZnRTaXplID0gc2l6ZXNbMF1cbiAgICAgIGNvbnN0IHJpZ2h0U2l6ZSA9IHNpemVzWzFdIHx8IDBcbiAgICAgIC8vIGNvbnN0IGFpUGFuZVNpemUgPSBzaXplc1syXSB8fCAwIC8vIEN1cnJlbnRseSB1bnVzZWRcblxuICAgICAgLy8gQ29sbGFwc2UgcmlnaHQgcGFuZWwgd2hlbiBsZWZ0IHBhbmVsIGV4Y2VlZHMgODUlIChsZWF2aW5nIHJvb20gZm9yIEFJIHBhbmUpXG4gICAgICBpZiAobGVmdFNpemUgPiA4NSAmJiAhaXNSaWdodFBhbmVsQ29sbGFwc2VkKSB7XG4gICAgICAgIHNldElzUmlnaHRQYW5lbENvbGxhcHNlZCh0cnVlKVxuICAgICAgICByaWdodFBhbmVsUmVmLmN1cnJlbnQ/LmNvbGxhcHNlKClcbiAgICAgIH0gZWxzZSBpZiAobGVmdFNpemUgPD0gODUgJiYgaXNSaWdodFBhbmVsQ29sbGFwc2VkICYmIHJpZ2h0U2l6ZSA9PT0gMCkge1xuICAgICAgICBzZXRJc1JpZ2h0UGFuZWxDb2xsYXBzZWQoZmFsc2UpXG4gICAgICAgIHJpZ2h0UGFuZWxSZWYuY3VycmVudD8uZXhwYW5kKClcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gMi1wYW5lbCBsYXlvdXQ6IGxlZnQsIHJpZ2h0IChvcmlnaW5hbCBsb2dpYylcbiAgICAgIGNvbnN0IGxlZnRTaXplID0gc2l6ZXNbMF1cbiAgICAgIGNvbnN0IHJpZ2h0U2l6ZSA9IHNpemVzWzFdIHx8IDBcblxuICAgICAgLy8gQ29sbGFwc2UgcmlnaHQgcGFuZWwgd2hlbiBsZWZ0IHBhbmVsIGV4Y2VlZHMgOTAlXG4gICAgICBpZiAobGVmdFNpemUgPiA5MCAmJiAhaXNSaWdodFBhbmVsQ29sbGFwc2VkKSB7XG4gICAgICAgIHNldElzUmlnaHRQYW5lbENvbGxhcHNlZCh0cnVlKVxuICAgICAgICByaWdodFBhbmVsUmVmLmN1cnJlbnQ/LmNvbGxhcHNlKClcbiAgICAgIH0gZWxzZSBpZiAobGVmdFNpemUgPD0gOTAgJiYgaXNSaWdodFBhbmVsQ29sbGFwc2VkICYmIHJpZ2h0U2l6ZSA9PT0gMCkge1xuICAgICAgICBzZXRJc1JpZ2h0UGFuZWxDb2xsYXBzZWQoZmFsc2UpXG4gICAgICAgIHJpZ2h0UGFuZWxSZWYuY3VycmVudD8uZXhwYW5kKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVTaG93Rmxvd1ZpZXcgPSAoKSA9PiB7XG4gICAgc2V0SXNSaWdodFBhbmVsQ29sbGFwc2VkKGZhbHNlKVxuICAgIHJpZ2h0UGFuZWxSZWYuY3VycmVudD8uZXhwYW5kKClcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVNob3dBaVBhbmUgPSAoKSA9PiB7XG4gICAgc2V0SXNBaVBhbmVDb2xsYXBzZWQoZmFsc2UpXG4gICAgYWlQYW5lUmVmLmN1cnJlbnQ/LmV4cGFuZCgpXG4gIH1cblxuICAvLyBDYWxjdWxhdGUgZGVmYXVsdCBzaXplcyBiYXNlZCBvbiB3aGV0aGVyIEFJIHBhbmUgaXMgc2hvd25cbiAgY29uc3QgZ2V0RGVmYXVsdFNpemVzID0gKCkgPT4ge1xuICAgIGlmIChzaG93QWlQYW5lICYmIGFpUGFuZSkge1xuICAgICAgLy8gMy1wYW5lbCBsYXlvdXQ6IGFkanVzdCBzaXplcyB0byBhY2NvbW1vZGF0ZSBBSSBwYW5lXG4gICAgICBjb25zdCBhaVBhbmVTaXplID0gMjUgLy8gQUkgcGFuZSBnZXRzIDI1JSBieSBkZWZhdWx0XG4gICAgICBjb25zdCByZW1haW5pbmcgPSAxMDAgLSBhaVBhbmVTaXplXG4gICAgICBjb25zdCBsZWZ0U2l6ZSA9IChkZWZhdWx0TGVmdFdpZHRoIC8gMTAwKSAqIHJlbWFpbmluZ1xuICAgICAgY29uc3QgcmlnaHRTaXplID0gcmVtYWluaW5nIC0gbGVmdFNpemVcbiAgICAgIHJldHVybiB7IGxlZnRTaXplLCByaWdodFNpemUsIGFpUGFuZVNpemUgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyAyLXBhbmVsIGxheW91dDogb3JpZ2luYWwgbG9naWNcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGxlZnRTaXplOiBkZWZhdWx0TGVmdFdpZHRoLFxuICAgICAgICByaWdodFNpemU6IDEwMCAtIGRlZmF1bHRMZWZ0V2lkdGgsXG4gICAgICAgIGFpUGFuZVNpemU6IDAsXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgY29uc3QgeyBsZWZ0U2l6ZSwgcmlnaHRTaXplLCBhaVBhbmVTaXplIH0gPSBnZXREZWZhdWx0U2l6ZXMoKVxuXG4gIC8vIE5vIGR5bmFtaWMgcG9zaXRpb25pbmcgbmVlZGVkIC0gYnV0dG9uIGlzIG5vdyBmaXhlZCBhdCBsZWZ0bW9zdCBwb3NpdGlvblxuXG4gIC8vIE1vYmlsZSBsYXlvdXQgd2l0aCB0YWJzXG4gIGlmIChpc01vYmlsZSkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtZnVsbCBtYXgtdy1mdWxsIHctZnVsbCByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICB7LyogTW9iaWxlIFJlY29tbWVuZGF0aW9uIEJhbm5lciAqL31cbiAgICAgICAge2RyYWdUcmVlSWQgJiYgKFxuICAgICAgICAgIDxNb2JpbGVSZWNvbW1lbmRhdGlvbkJhbm5lclxuICAgICAgICAgICAgZHJhZ1RyZWVJZD17ZHJhZ1RyZWVJZH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIE1vYmlsZSBUYWIgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgPE1vYmlsZVRhYk5hdmlnYXRpb25cbiAgICAgICAgICBhY3RpdmVUYWI9e2FjdGl2ZVRhYn1cbiAgICAgICAgICBvblRhYkNoYW5nZT17c2V0QWN0aXZlVGFifVxuICAgICAgICAgIHNob3dBaVRhYj17c2hvd0FpUGFuZX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCJcbiAgICAgICAgLz5cblxuICAgICAgICB7LyogTW9iaWxlIFBhbmVsIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LWhpZGRlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgey8qIFRyZWUgUGFuZWwgKi99XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgJ2gtZnVsbCBvdmVyZmxvdy1oaWRkZW4gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwJyxcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIGJsb2NrOiBhY3RpdmVUYWIgPT09ICd0cmVlJyxcbiAgICAgICAgICAgICAgICBoaWRkZW46IGFjdGl2ZVRhYiAhPT0gJ3RyZWUnLFxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgcm9sZT1cInRhYnBhbmVsXCJcbiAgICAgICAgICAgIGlkPVwidHJlZS1wYW5lbFwiXG4gICAgICAgICAgICBhcmlhLWxhYmVsbGVkYnk9XCJ0cmVlLXRhYlwiXG4gICAgICAgICAgICBhcmlhLWxhYmVsPVwiVHJlZSBvdXRsaW5lIHZpZXdcIlxuICAgICAgICAgICAgZGF0YS1wYW5lPVwib3V0bGluZVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgcC0yXCI+XG4gICAgICAgICAgICAgIDxQYW5lRXJyb3JCb3VuZGFyeVxuICAgICAgICAgICAgICAgIHBhbmVUeXBlPVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgb25SZXRyeT17KCkgPT4gaGFuZGxlUGFuZVJldHJ5KCdvdXRsaW5lJyl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7bGVmdFBhbmVsfVxuICAgICAgICAgICAgICA8L1BhbmVFcnJvckJvdW5kYXJ5PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGlhZ3JhbSBQYW5lbCAqL31cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAnaC1mdWxsIG92ZXJmbG93LWhpZGRlbiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAnLFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgYmxvY2s6IGFjdGl2ZVRhYiA9PT0gJ2RpYWdyYW0nLFxuICAgICAgICAgICAgICAgIGhpZGRlbjogYWN0aXZlVGFiICE9PSAnZGlhZ3JhbScsXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICByb2xlPVwidGFicGFuZWxcIlxuICAgICAgICAgICAgaWQ9XCJkaWFncmFtLXBhbmVsXCJcbiAgICAgICAgICAgIGFyaWEtbGFiZWxsZWRieT1cImRpYWdyYW0tdGFiXCJcbiAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJWaXN1YWwgZmxvdyBkaWFncmFtIHZpZXdcIlxuICAgICAgICAgICAgZGF0YS1wYW5lPVwidmlzdWFsaXphdGlvblwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgcC0yXCI+XG4gICAgICAgICAgICAgIDxQYW5lRXJyb3JCb3VuZGFyeVxuICAgICAgICAgICAgICAgIHBhbmVUeXBlPVwidmlzdWFsaXphdGlvblwiXG4gICAgICAgICAgICAgICAgb25SZXRyeT17KCkgPT4gaGFuZGxlUGFuZVJldHJ5KCd2aXN1YWxpemF0aW9uJyl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7cmlnaHRQYW5lbH1cbiAgICAgICAgICAgICAgPC9QYW5lRXJyb3JCb3VuZGFyeT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEFJIFBhbmVsICovfVxuICAgICAgICAgIHtzaG93QWlQYW5lICYmIGFpUGFuZSAmJiAoXG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgJ2gtZnVsbCBvdmVyZmxvdy1oaWRkZW4gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwJyxcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICBibG9jazogYWN0aXZlVGFiID09PSAnYWknLFxuICAgICAgICAgICAgICAgICAgaGlkZGVuOiBhY3RpdmVUYWIgIT09ICdhaScsXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICByb2xlPVwidGFicGFuZWxcIlxuICAgICAgICAgICAgICBpZD1cImFpLXBhbmVsXCJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbGxlZGJ5PVwiYWktdGFiXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGxcIj57YWlQYW5lfTwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyBEZXNrdG9wIGxheW91dCB3aXRoIHJlc2l6YWJsZSBwYW5lbHNcbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICByZWY9e2NvbnRhaW5lclJlZn1cbiAgICAgIGNsYXNzTmFtZT1cImgtZnVsbCBtYXgtdy1mdWxsIHctZnVsbCByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gcC00IHBiLTIgZmxleCBmbGV4LWNvbCBtaW4taC0wXCJcbiAgICA+XG5cbiAgICAgIDxSZXNpemFibGVQYW5lbEdyb3VwXG4gICAgICAgIGRpcmVjdGlvbj1cImhvcml6b250YWxcIlxuICAgICAgICBvbkxheW91dD17aGFuZGxlUGFuZWxSZXNpemV9XG4gICAgICAgIGNsYXNzTmFtZT1cImgtZnVsbFwiXG4gICAgICA+XG4gICAgICAgIHsvKiBMZWZ0IFBhbmVsIC0gVHJlZSBWaWV3IChBbHdheXMgdmlzaWJsZSkgKi99XG4gICAgICAgIDxSZXNpemFibGVQYW5lbFxuICAgICAgICAgIGRlZmF1bHRTaXplPXtsZWZ0U2l6ZX1cbiAgICAgICAgICBtaW5TaXplPXttaW5MZWZ0V2lkdGh9XG4gICAgICAgICAgbWF4U2l6ZT17XG4gICAgICAgICAgICBzaG93QWlQYW5lICYmIGFpUGFuZVxuICAgICAgICAgICAgICA/IDEwMCAtIG1pblJpZ2h0V2lkdGggLSBtaW5BaVBhbmVXaWR0aFxuICAgICAgICAgICAgICA6IDEwMCAtIG1pblJpZ2h0V2lkdGhcbiAgICAgICAgICB9XG4gICAgICAgICAgY2xhc3NOYW1lPVwib3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIHJvbGU9XCJyZWdpb25cIlxuICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlRyZWUgb3V0bGluZSB2aWV3XCJcbiAgICAgICAgICAgIGRhdGEtcGFuZT1cIm91dGxpbmVcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgJ2gtZnVsbCcsXG4gICAgICAgICAgICAgIHNob3dBaVBhbmUgPyAnZmlsdGVyIGJsdXItc20gb3BhY2l0eS02MCBwb2ludGVyLWV2ZW50cy1ub25lJyA6ICcnXG4gICAgICAgICAgICApfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxQYW5lRXJyb3JCb3VuZGFyeVxuICAgICAgICAgICAgICBwYW5lVHlwZT1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvblJldHJ5PXsoKSA9PiBoYW5kbGVQYW5lUmV0cnkoJ291dGxpbmUnKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzRmxpcHBlZCA/IHJpZ2h0UGFuZWwgOiBsZWZ0UGFuZWx9XG4gICAgICAgICAgICA8L1BhbmVFcnJvckJvdW5kYXJ5PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L1Jlc2l6YWJsZVBhbmVsPlxuXG4gICAgICAgIHsvKiBGaXJzdCBSZXNpemFibGUgSGFuZGxlIC0gQWx3YXlzIHJlbmRlcmVkICovfVxuICAgICAgICA8UmVzaXphYmxlSGFuZGxlXG4gICAgICAgICAgaWQ9XCJ0dXRvcmlhbC1yZXNpemUtaGFuZGxlXCJcbiAgICAgICAgICB3aXRoSGFuZGxlXG4gICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICd3LTEgYmctZ3JheS0yMDAgaG92ZXI6YmctYmx1ZS00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGN1cnNvci1jb2wtcmVzaXplJyxcbiAgICAgICAgICAgICdkYXRhLVtyZXNpemUtaGFuZGxlLXN0YXRlPWhvdmVyXTpiZy1ibHVlLTUwMCcsXG4gICAgICAgICAgICAnZGF0YS1bcmVzaXplLWhhbmRsZS1zdGF0ZT1kcmFnXTpiZy1ibHVlLTUwMCdcbiAgICAgICAgICApfVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBSaWdodCBQYW5lbCAtIFJlYWN0IEZsb3cgKENhbiBiZSBjb2xsYXBzZWQpICovfVxuICAgICAgICA8UmVzaXphYmxlUGFuZWxcbiAgICAgICAgICByZWY9e3JpZ2h0UGFuZWxSZWZ9XG4gICAgICAgICAgZGVmYXVsdFNpemU9e3JpZ2h0U2l6ZX1cbiAgICAgICAgICBtaW5TaXplPXttaW5SaWdodFdpZHRofVxuICAgICAgICAgIGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlblwiXG4gICAgICAgICAgY29sbGFwc2libGU9e3RydWV9XG4gICAgICAgICAgb25Db2xsYXBzZT17KCkgPT4gc2V0SXNSaWdodFBhbmVsQ29sbGFwc2VkKHRydWUpfVxuICAgICAgICAgIG9uRXhwYW5kPXsoKSA9PiBzZXRJc1JpZ2h0UGFuZWxDb2xsYXBzZWQoZmFsc2UpfVxuICAgICAgICA+XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgcm9sZT1cInJlZ2lvblwiXG4gICAgICAgICAgICBhcmlhLWxhYmVsPVwiVmlzdWFsIGZsb3cgZGlhZ3JhbSB2aWV3XCJcbiAgICAgICAgICAgIGRhdGEtcGFuZT1cInZpc3VhbGl6YXRpb25cIlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgJ2gtZnVsbCcsXG4gICAgICAgICAgICAgIHNob3dBaVBhbmUgPyAnZmlsdGVyIGJsdXItc20gb3BhY2l0eS02MCBwb2ludGVyLWV2ZW50cy1ub25lJyA6ICcnXG4gICAgICAgICAgICApfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxQYW5lRXJyb3JCb3VuZGFyeVxuICAgICAgICAgICAgICBwYW5lVHlwZT1cInZpc3VhbGl6YXRpb25cIlxuICAgICAgICAgICAgICBvblJldHJ5PXsoKSA9PiBoYW5kbGVQYW5lUmV0cnkoJ3Zpc3VhbGl6YXRpb24nKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzRmxpcHBlZCA/IGxlZnRQYW5lbCA6IHJpZ2h0UGFuZWx9XG4gICAgICAgICAgICA8L1BhbmVFcnJvckJvdW5kYXJ5PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L1Jlc2l6YWJsZVBhbmVsPlxuXG4gICAgICAgIHsvKiBBSSBQYW5lIFNlY3Rpb24gLSBPbmx5IHJlbmRlciBpZiBhaVBhbmUgaXMgcHJvdmlkZWQgYW5kIHNob3dBaVBhbmUgaXMgdHJ1ZSAqL31cbiAgICAgICAge3Nob3dBaVBhbmUgJiYgYWlQYW5lICYmIChcbiAgICAgICAgICA8PlxuICAgICAgICAgICAgey8qIFNlY29uZCBSZXNpemFibGUgSGFuZGxlIC0gRm9yIEFJIFBhbmUgKi99XG4gICAgICAgICAgICA8UmVzaXphYmxlSGFuZGxlXG4gICAgICAgICAgICAgIGlkPVwiYWktcGFuZS1yZXNpemUtaGFuZGxlXCJcbiAgICAgICAgICAgICAgd2l0aEhhbmRsZVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICd3LTEgYmctZ3JheS0yMDAgaG92ZXI6YmctYmx1ZS00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGN1cnNvci1jb2wtcmVzaXplJyxcbiAgICAgICAgICAgICAgICAnZGF0YS1bcmVzaXplLWhhbmRsZS1zdGF0ZT1ob3Zlcl06YmctYmx1ZS01MDAnLFxuICAgICAgICAgICAgICAgICdkYXRhLVtyZXNpemUtaGFuZGxlLXN0YXRlPWRyYWddOmJnLWJsdWUtNTAwJ1xuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgey8qIEFJIFBhbmUgLSBDYW4gYmUgY29sbGFwc2VkICovfVxuICAgICAgICAgICAgPFJlc2l6YWJsZVBhbmVsXG4gICAgICAgICAgICAgIHJlZj17YWlQYW5lUmVmfVxuICAgICAgICAgICAgICBkZWZhdWx0U2l6ZT17YWlQYW5lU2l6ZX1cbiAgICAgICAgICAgICAgbWluU2l6ZT17bWluQWlQYW5lV2lkdGh9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlbiBtaW4tdy1bMjByZW1dXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgdHJhbnNpdGlvbjogJ2ZsZXgtYmFzaXMgMC41cyBlYXNlLWluLW91dCcgfX1cbiAgICAgICAgICAgICAgY29sbGFwc2libGU9e3RydWV9XG4gICAgICAgICAgICAgIG9uQ29sbGFwc2U9eygpID0+IHNldElzQWlQYW5lQ29sbGFwc2VkKHRydWUpfVxuICAgICAgICAgICAgICBvbkV4cGFuZD17KCkgPT4gc2V0SXNBaVBhbmVDb2xsYXBzZWQoZmFsc2UpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGlkPVwidHV0b3JpYWwtYWktcGFuZVwiIGNsYXNzTmFtZT1cImgtZnVsbFwiPlxuICAgICAgICAgICAgICAgIHthaVBhbmV9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9SZXNpemFibGVQYW5lbD5cbiAgICAgICAgICA8Lz5cbiAgICAgICAgKX1cbiAgICAgIDwvUmVzaXphYmxlUGFuZWxHcm91cD5cblxuICAgICAgey8qIFJlc2V0IEJ1dHRvbnMgKi99XG4gICAgICB7aXNSaWdodFBhbmVsQ29sbGFwc2VkICYmIChcbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNob3dGbG93Vmlld31cbiAgICAgICAgICBjbGFzc05hbWU9XCJcbiAgICAgICAgICAgIGFic29sdXRlIHRvcC00IHJpZ2h0LTQgei0xMCBweC0zIHB5LTEgYmctYmx1ZS01MDAgdGV4dC13aGl0ZSB0ZXh0LXNtXG4gICAgICAgICAgICByb3VuZGVkLW1kIGhvdmVyOmJnLWJsdWUtNjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFxuICAgICAgICAgIFwiXG4gICAgICAgID5cbiAgICAgICAgICB7aXNGbGlwcGVkID8gJ1Nob3cgT3V0bGluZSBWaWV3JyA6ICdTaG93IEZsb3cgVmlldyd9XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgKX1cblxuICAgICAge3Nob3dBaVBhbmUgJiYgYWlQYW5lICYmIGlzQWlQYW5lQ29sbGFwc2VkICYmIChcbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNob3dBaVBhbmV9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiXG4gICAgICAgICAgICBhYnNvbHV0ZSB0b3AtMTIgcmlnaHQtNCB6LTEwIHB4LTMgcHktMSBiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHRleHQtc21cbiAgICAgICAgICAgIHJvdW5kZWQtbWQgaG92ZXI6YmctYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXG4gICAgICAgICAgXCJcbiAgICAgICAgPlxuICAgICAgICAgIFNob3cgQUkgQXNzaXN0YW50XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJjbiIsIlJlc2l6YWJsZVBhbmVsR3JvdXAiLCJSZXNpemFibGVQYW5lbCIsIlJlc2l6YWJsZUhhbmRsZSIsInVzZUlzTW9iaWxlIiwiTW9iaWxlVGFiTmF2aWdhdGlvbiIsIk1vYmlsZVJlY29tbWVuZGF0aW9uQmFubmVyIiwiUGFuZUVycm9yQm91bmRhcnkiLCJ1c2VLZXlib2FyZE5hdmlnYXRpb24iLCJSZXNpemFibGVMYXlvdXQiLCJsZWZ0UGFuZWwiLCJyaWdodFBhbmVsIiwiYWlQYW5lIiwibWluTGVmdFdpZHRoIiwibWluUmlnaHRXaWR0aCIsIm1pbkFpUGFuZVdpZHRoIiwiZGVmYXVsdExlZnRXaWR0aCIsInNob3dBaVBhbmUiLCJkcmFnVHJlZUlkIiwiaXNNb2JpbGUiLCJpc1JpZ2h0UGFuZWxDb2xsYXBzZWQiLCJzZXRJc1JpZ2h0UGFuZWxDb2xsYXBzZWQiLCJpc0FpUGFuZUNvbGxhcHNlZCIsInNldElzQWlQYW5lQ29sbGFwc2VkIiwiY29udGFpbmVyUmVmIiwiTE9DQUxfS0VZIiwiaXNGbGlwcGVkIiwic2V0SXNGbGlwcGVkIiwid2luZG93IiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInRvZ2dsZUZsaXAiLCJwcmV2IiwibmV4dCIsInNldEl0ZW0iLCJyaWdodFBhbmVsUmVmIiwiYWlQYW5lUmVmIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiZW5hYmxlS2V5Ym9hcmROYXZpZ2F0aW9uIiwiZW5hYmxlRm9jdXNNYW5hZ2VtZW50IiwiYW5ub3VuY2VOYXZpZ2F0aW9uQ2hhbmdlcyIsImhhbmRsZVBhbmVSZXRyeSIsInBhbmVUeXBlIiwiZXZlbnQiLCJDdXN0b21FdmVudCIsImRldGFpbCIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJkaXNwYXRjaEV2ZW50Iiwic2V0VGltZW91dCIsImNvbmZpcm0iLCJsb2NhdGlvbiIsInJlbG9hZCIsImhhbmRsZVBhbmVsUmVzaXplIiwic2l6ZXMiLCJsZWZ0U2l6ZSIsInJpZ2h0U2l6ZSIsImN1cnJlbnQiLCJjb2xsYXBzZSIsImV4cGFuZCIsImhhbmRsZVNob3dGbG93VmlldyIsImhhbmRsZVNob3dBaVBhbmUiLCJnZXREZWZhdWx0U2l6ZXMiLCJhaVBhbmVTaXplIiwicmVtYWluaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwib25UYWJDaGFuZ2UiLCJzaG93QWlUYWIiLCJibG9jayIsImhpZGRlbiIsInJvbGUiLCJpZCIsImFyaWEtbGFiZWxsZWRieSIsImFyaWEtbGFiZWwiLCJkYXRhLXBhbmUiLCJvblJldHJ5IiwicmVmIiwiZGlyZWN0aW9uIiwib25MYXlvdXQiLCJkZWZhdWx0U2l6ZSIsIm1pblNpemUiLCJtYXhTaXplIiwid2l0aEhhbmRsZSIsImNvbGxhcHNpYmxlIiwib25Db2xsYXBzZSIsIm9uRXhwYW5kIiwic3R5bGUiLCJ0cmFuc2l0aW9uIiwiYnV0dG9uIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout.tsx\n"));

/***/ })

});