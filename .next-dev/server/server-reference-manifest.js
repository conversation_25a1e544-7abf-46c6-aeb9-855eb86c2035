self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"4018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"4019c1b8a33c34312f8b2b4925319e4bb690b96ee0\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"40329d702a7801878a6e224b0c5ed0520679a7fb4f\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"403a225344cb56f415f151179006409721e36e0fe2\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"40a13ab81e87a28697e32191ff2ce5acfe51abb6a5\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"40ad43c217f93ef8e85adc7b46975d968bf3dd6b14\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"40b5a460aeb8a1d1bb23a0572fa0bd600788c456cf\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"40bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"40bc9b18c31c301cc60fdf18ff3f38691775487d3a\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"40c046edca06bb8e5745142899b217fce36f941bbf\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"40db82d3a345a473ff256530b559e4cfebd8d5a102\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"6019c9c8b40bd9fd86cec10e24d36bed040dcbcd75\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"60a2f155b9d4c4d38c716560a36e13f436acf3922c\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"60dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"60f2c7ec743645df5666ac3342ff908a5520fad8ac\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"789bc08d0620bd72586bc182f9b829cfe80e22fffb\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Findex.ts%22%2C%5B%7B%22id%22%3A%224018f1f14c3fe9b5d8c04ebfab98fe5661d345e43f%22%2C%22exportedName%22%3A%22deleteDragTree%22%7D%2C%7B%22id%22%3A%224019c1b8a33c34312f8b2b4925319e4bb690b96ee0%22%2C%22exportedName%22%3A%22batchCreateDragTreeNodes%22%7D%2C%7B%22id%22%3A%2240329d702a7801878a6e224b0c5ed0520679a7fb4f%22%2C%22exportedName%22%3A%22updateDragTreeNode%22%7D%2C%7B%22id%22%3A%22403a225344cb56f415f151179006409721e36e0fe2%22%2C%22exportedName%22%3A%22getInitialPageData%22%7D%2C%7B%22id%22%3A%2240a13ab81e87a28697e32191ff2ce5acfe51abb6a5%22%2C%22exportedName%22%3A%22getDragTreeByUserId%22%7D%2C%7B%22id%22%3A%2240ad43c217f93ef8e85adc7b46975d968bf3dd6b14%22%2C%22exportedName%22%3A%22createDragTree%22%7D%2C%7B%22id%22%3A%2240b5a460aeb8a1d1bb23a0572fa0bd600788c456cf%22%2C%22exportedName%22%3A%22updateDragTree%22%7D%2C%7B%22id%22%3A%2240bc314ca6adc67f2ae276f3dd2d42a5d643b5f0f8%22%2C%22exportedName%22%3A%22createDragTreeNode%22%7D%2C%7B%22id%22%3A%2240bc9b18c31c301cc60fdf18ff3f38691775487d3a%22%2C%22exportedName%22%3A%22deleteDragTreeNode%22%7D%2C%7B%22id%22%3A%2240c046edca06bb8e5745142899b217fce36f941bbf%22%2C%22exportedName%22%3A%22getDragTreesByUserId%22%7D%2C%7B%22id%22%3A%2240db82d3a345a473ff256530b559e4cfebd8d5a102%22%2C%22exportedName%22%3A%22createDragTreeNodeContent%22%7D%2C%7B%22id%22%3A%226019c9c8b40bd9fd86cec10e24d36bed040dcbcd75%22%2C%22exportedName%22%3A%22getDragTree%22%7D%2C%7B%22id%22%3A%2260a2f155b9d4c4d38c716560a36e13f436acf3922c%22%2C%22exportedName%22%3A%22updateDragTreeLanguage%22%7D%2C%7B%22id%22%3A%2260dc60e776ffb982c3c6d4d12ce46fb27afdab3e2a%22%2C%22exportedName%22%3A%22updateDragTreeTitle%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generations.ts%22%2C%5B%7B%22id%22%3A%2260f2c7ec743645df5666ac3342ff908a5520fad8ac%22%2C%22exportedName%22%3A%22getAIGenerationsCount%22%7D%2C%7B%22id%22%3A%22789bc08d0620bd72586bc182f9b829cfe80e22fffb%22%2C%22exportedName%22%3A%22getAIGenerations%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"rsc\"\n      }\n    },\n    \"6017ef769926e21dc0dc3d2e2890c7ac421c2dce1e\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"4095afb0486c153392482016b9e0dbeb7146ca4d1a\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"4071f162819d90372b191761a07599d364616cff87\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"40ace940b87ae22f5d66a37eef568b1d387f1defa2\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"408d2af4b47b101c1484da00fe7596341b4f4efcb6\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"605221564d98e391eb37e9dd78e4207a45f28f0073\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"60fa22caa77e124bf7e67bf914328be49658e3772f\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"7023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"705b6b663451f592d8aba27204702cccf3ec52fe07\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"706419648d264f254622b0638a181b2facdace52fa\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"40749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"40c212dca6995f1c98b5302f2aac40d1d2ca37c197\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"401980d5f98c5f5f6725e2054682709cd6939a1976\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"40426edb85d195a8671132cb6ea3b5b89874fe4ea3\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"40734770377f903d5d6b39202f1dd59cad0fc5bb98\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"4091acf03841114ceb4487dc59349f4f608a1ea213\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    },\n    \"40fa9710759ddb1879a8806e468e9acb7b5af70b61\": {\n      \"workers\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_node_contents_batch.ts%22%2C%5B%7B%22id%22%3A%224095afb0486c153392482016b9e0dbeb7146ca4d1a%22%2C%22exportedName%22%3A%22getNodeContentsBatch%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fresearch-update.ts%22%2C%5B%7B%22id%22%3A%226017ef769926e21dc0dc3d2e2890c7ac421c2dce1e%22%2C%22exportedName%22%3A%22updateDragTreeNodeContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget-tree-structure.ts%22%2C%5B%7B%22id%22%3A%224071f162819d90372b191761a07599d364616cff87%22%2C%22exportedName%22%3A%22getDragTreeStructure%22%7D%2C%7B%22id%22%3A%22409e1a88607dd51df88ed8f3e9ab89f8d57203e3d0%22%2C%22exportedName%22%3A%22getNodeContentOnDemand%22%7D%2C%7B%22id%22%3A%2240ace940b87ae22f5d66a37eef568b1d387f1defa2%22%2C%22exportedName%22%3A%22comparePayloadSizes%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fupdate_ai_generation.ts%22%2C%5B%7B%22id%22%3A%22408d2af4b47b101c1484da00fe7596341b4f4efcb6%22%2C%22exportedName%22%3A%22markAIConversationDeleted%22%7D%2C%7B%22id%22%3A%22605221564d98e391eb37e9dd78e4207a45f28f0073%22%2C%22exportedName%22%3A%22markAIGenerationDeleted%22%7D%2C%7B%22id%22%3A%2260fa22caa77e124bf7e67bf914328be49658e3772f%22%2C%22exportedName%22%3A%22markAIGenerationRead%22%7D%2C%7B%22id%22%3A%227023d87bebb1e68e5b9e988b3e262c5d9bfcc3a02e%22%2C%22exportedName%22%3A%22updateAIGeneration%22%7D%2C%7B%22id%22%3A%22705b6b663451f592d8aba27204702cccf3ec52fe07%22%2C%22exportedName%22%3A%22updateAIGenerationTitle%22%7D%2C%7B%22id%22%3A%22706419648d264f254622b0638a181b2facdace52fa%22%2C%22exportedName%22%3A%22updateAIGenerationFromTiptap%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fdrag-tree%2Fget_ai_generation_content.ts%22%2C%5B%7B%22id%22%3A%2240749a2106e7c1c9eeb3857a7ffaa4cb8eda03eb89%22%2C%22exportedName%22%3A%22checkAIGenerationAccess%22%7D%2C%7B%22id%22%3A%2240c212dca6995f1c98b5302f2aac40d1d2ca37c197%22%2C%22exportedName%22%3A%22getAIGenerationContent%22%7D%5D%5D%2C%5B%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fserver-actions%2Fuser.ts%22%2C%5B%7B%22id%22%3A%22401980d5f98c5f5f6725e2054682709cd6939a1976%22%2C%22exportedName%22%3A%22markTutorialCompleted%22%7D%2C%7B%22id%22%3A%2240426edb85d195a8671132cb6ea3b5b89874fe4ea3%22%2C%22exportedName%22%3A%22getUserMetadata%22%7D%2C%7B%22id%22%3A%22404cd16eac9824ff750b2f23ac3fc3ac8fa8bbe6a9%22%2C%22exportedName%22%3A%22updateUserMetadata%22%7D%2C%7B%22id%22%3A%2240734770377f903d5d6b39202f1dd59cad0fc5bb98%22%2C%22exportedName%22%3A%22testTutorialPersistence%22%7D%2C%7B%22id%22%3A%224091acf03841114ceb4487dc59349f4f608a1ea213%22%2C%22exportedName%22%3A%22checkTutorialCompleted%22%7D%2C%7B%22id%22%3A%22409dadb7d0483f4ac924ead6f203f7b9f1cc5f5715%22%2C%22exportedName%22%3A%22checkTutorialStatus%22%7D%2C%7B%22id%22%3A%2240fa9710759ddb1879a8806e468e9acb7b5af70b61%22%2C%22exportedName%22%3A%22markTutorialSkipped%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(conv)/dragTree/[dragTreeId]/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"