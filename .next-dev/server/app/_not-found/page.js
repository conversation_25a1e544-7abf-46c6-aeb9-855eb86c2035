/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./app/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/github/next13-clarify/app/context/AuthContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/github/next13-clarify/app/context/AuthContext.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/context/ToasterContext.tsx":
/*!****************************************!*\
  !*** ./app/context/ToasterContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/github/next13-clarify/app/context/ToasterContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/github/next13-clarify/app/context/ToasterContext.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"36b21640ebb1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9nZW9mZnJleWh1bmcvRGVza3RvcC9naXRodWIvbmV4dDEzLWNsYXJpZnkvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzZiMjE2NDBlYmIxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _styles_prosemirror_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/prosemirror.css */ \"(rsc)/./styles/prosemirror.css\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/AuthContext */ \"(rsc)/./app/context/AuthContext.tsx\");\n/* harmony import */ var _context_ToasterContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./context/ToasterContext */ \"(rsc)/./app/context/ToasterContext.tsx\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _vercel_analytics_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @vercel/analytics/react */ \"(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'ThinkGraph - AI-Powered Structured Thinking',\n    description: 'Transform complex problems into clear, actionable insights with ThinkGraph. Build interactive thinking graphs that help you explore every angle before making decisions. Perfect for deep thinkers, consultants, and knowledge workers.',\n    keywords: [\n        'ThinkGraph',\n        'structured thinking',\n        'AI-powered decision making',\n        'interactive thinking graphs',\n        'problem exploration tool',\n        'decision support system',\n        'complex problem solving',\n        'AI-assisted brainstorming',\n        'strategic planning tool',\n        'thought organization',\n        'knowledge worker productivity',\n        'deep thinking tool',\n        'comprehensive analysis',\n        'decision tree builder',\n        'AI for consultants',\n        'management decision support',\n        'systematic thinking',\n        'problem decomposition',\n        'insight generation',\n        'ChatGPT enhancement',\n        'AI productivity tool',\n        'INTP thinking tool',\n        'overthinking solution'\n    ],\n    icons: {\n        icon: '/favicon.ico',\n        shortcut: '/images/thinkgraph-favicon.svg',\n        apple: '/images/thinkgraph-icon.svg'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: 'ThinkGraph - AI-Powered Structured Thinking'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: metadata.description || ''\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: Array.isArray(metadata.keywords) ? metadata.keywords.join(', ') : ''\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className)} h-full w-full flex flex-col`,\n                suppressHydrationWarning: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vercel_analytics_react__WEBPACK_IMPORTED_MODULE_5__.Analytics, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_ToasterContext__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppContext: () => (/* binding */ AppContext),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AppContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AppContext() from the server but AppContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/github/next13-clarify/app/providers.tsx",
"AppContext",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/github/next13-clarify/app/providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/github/next13-clarify/app/providers.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"/Users/<USER>/Desktop/github/next13-clarify/app/layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FToasterContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2F%40vercel%2Fanalytics%2Fdist%2Freact%2Findex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fstyles%2Fprosemirror.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FToasterContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2F%40vercel%2Fanalytics%2Fdist%2Freact%2Findex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fstyles%2Fprosemirror.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/AuthContext.tsx */ \"(rsc)/./app/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/ToasterContext.tsx */ \"(rsc)/./app/context/ToasterContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(rsc)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/analytics/dist/react/index.mjs */ \"(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZ2VvZmZyZXlodW5nJTJGRGVza3RvcCUyRmdpdGh1YiUyRm5leHQxMy1jbGFyaWZ5JTJGYXBwJTJGY29udGV4dCUyRkF1dGhDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZnZW9mZnJleWh1bmclMkZEZXNrdG9wJTJGZ2l0aHViJTJGbmV4dDEzLWNsYXJpZnklMkZhcHAlMkZjb250ZXh0JTJGVG9hc3RlckNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmdlb2ZmcmV5aHVuZyUyRkRlc2t0b3AlMkZnaXRodWIlMkZuZXh0MTMtY2xhcmlmeSUyRmFwcCUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZ2VvZmZyZXlodW5nJTJGRGVza3RvcCUyRmdpdGh1YiUyRm5leHQxMy1jbGFyaWZ5JTJGYXBwJTJGcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZnZW9mZnJleWh1bmclMkZEZXNrdG9wJTJGZ2l0aHViJTJGbmV4dDEzLWNsYXJpZnklMkZub2RlX21vZHVsZXMlMkYlNDB2ZXJjZWwlMkZhbmFseXRpY3MlMkZkaXN0JTJGcmVhY3QlMkZpbmRleC5tanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBbmFseXRpY3MlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZnZW9mZnJleWh1bmclMkZEZXNrdG9wJTJGZ2l0aHViJTJGbmV4dDEzLWNsYXJpZnklMkZzdHlsZXMlMkZwcm9zZW1pcnJvci5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZnZW9mZnJleWh1bmclMkZEZXNrdG9wJTJGZ2l0aHViJTJGbmV4dDEzLWNsYXJpZnklMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUErSTtBQUMvSTtBQUNBLDRLQUFrSjtBQUNsSjtBQUNBLGtKQUFxSTtBQUNySTtBQUNBLHNOQUF5SyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9nZW9mZnJleWh1bmcvRGVza3RvcC9naXRodWIvbmV4dDEzLWNsYXJpZnkvYXBwL2NvbnRleHQvQXV0aENvbnRleHQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2dlb2ZmcmV5aHVuZy9EZXNrdG9wL2dpdGh1Yi9uZXh0MTMtY2xhcmlmeS9hcHAvY29udGV4dC9Ub2FzdGVyQ29udGV4dC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvZ2VvZmZyZXlodW5nL0Rlc2t0b3AvZ2l0aHViL25leHQxMy1jbGFyaWZ5L2FwcC9wcm92aWRlcnMudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBbmFseXRpY3NcIl0gKi8gXCIvVXNlcnMvZ2VvZmZyZXlodW5nL0Rlc2t0b3AvZ2l0aHViL25leHQxMy1jbGFyaWZ5L25vZGVfbW9kdWxlcy9AdmVyY2VsL2FuYWx5dGljcy9kaXN0L3JlYWN0L2luZGV4Lm1qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FToasterContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2F%40vercel%2Fanalytics%2Fdist%2Freact%2Findex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fstyles%2Fprosemirror.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./styles/prosemirror.css":
/*!********************************!*\
  !*** ./styles/prosemirror.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"265cde1a50a8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvcHJvc2VtaXJyb3IuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvZ2VvZmZyZXlodW5nL0Rlc2t0b3AvZ2l0aHViL25leHQxMy1jbGFyaWZ5L3N0eWxlcy9wcm9zZW1pcnJvci5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyNjVjZGUxYTUwYThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./styles/prosemirror.css\n");

/***/ }),

/***/ "(ssr)/./app/components/PostHogUserIdentifier.tsx":
/*!**************************************************!*\
  !*** ./app/components/PostHogUserIdentifier.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PostHogUserIdentifier)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_libs_posthog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/libs/posthog */ \"(ssr)/./app/libs/posthog.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/**\n * Minimal PostHog user identification.\n * Uses NextAuth's useSession to get user.id.\n * Sends only user id and status (optional).\n */ function PostHogUserIdentifier() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const identifiedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PostHogUserIdentifier.useEffect\": ()=>{\n            if (!identifiedRef.current && status === 'authenticated' && session?.user?.id) {\n                (0,_app_libs_posthog__WEBPACK_IMPORTED_MODULE_2__.identifyUser)(session.user.id, {\n                    user_status: session.user.status\n                });\n                identifiedRef.current = true;\n            }\n        }\n    }[\"PostHogUserIdentifier.useEffect\"], [\n        session,\n        status\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/PostHogUserIdentifier.tsx\n");

/***/ }),

/***/ "(ssr)/./app/configs/index.ts":
/*!******************************!*\
  !*** ./app/configs/index.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageEnum: () => (/* binding */ LanguageEnum),\n/* harmony export */   activeConversationCutoffDate: () => (/* binding */ activeConversationCutoffDate),\n/* harmony export */   chatgptSiteUrl: () => (/* binding */ chatgptSiteUrl),\n/* harmony export */   claudeSiteUrl: () => (/* binding */ claudeSiteUrl),\n/* harmony export */   contactEmail: () => (/* binding */ contactEmail),\n/* harmony export */   copilotSiteUrl: () => (/* binding */ copilotSiteUrl),\n/* harmony export */   geminiSiteUrl: () => (/* binding */ geminiSiteUrl),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   maxActiveConversations: () => (/* binding */ maxActiveConversations),\n/* harmony export */   minChars: () => (/* binding */ minChars),\n/* harmony export */   modelServiceProvider: () => (/* binding */ modelServiceProvider),\n/* harmony export */   notebookCountLimit: () => (/* binding */ notebookCountLimit),\n/* harmony export */   numberOfLinksToUse: () => (/* binding */ numberOfLinksToUse),\n/* harmony export */   youtubeEmbedUrlId: () => (/* binding */ youtubeEmbedUrlId)\n/* harmony export */ });\n// The max allowed ACTIVE conversations\n// Mainly avoid abuse or weird bug that leads to infinite loop\nconst maxActiveConversations = 5;\n// nth really special about this date.\n// Just we have previously launched the product, if we find the old users to try again\n// It is better to grant them more conversations to try it\n// If ever we launch again, we can change this date, but ensure we have documented the change HERE in the code comment\nconst activeConversationCutoffDate = new Date(\"2024-10-01\");\n// Min char required to start rephrase or clarify, used in EmptyState\nconst minChars = 30;\n// Define as constant here, so if we have feature that point to other site, we can easily do it\nconst chatgptSiteUrl = \"https://chat.openai.com\";\nconst claudeSiteUrl = \"https://claude.ai\";\nconst copilotSiteUrl = \"https://copilot.microsoft.com/?showntbk=1\";\nconst geminiSiteUrl = \"https://gemini.google.com\";\n// When we do rag generative search, how many links to use\n// 4 means we consider the top 4 links only\n// Adding this number will consume more tokens, max is 9\nconst numberOfLinksToUse = 4;\nconst contactEmail = \"<EMAIL>\";\n// alternative is openai, decide which API service to use\n// We got Azure credits, let's use it first, we can redeem OpenAI / more Azure later\nconst modelServiceProvider = \"azure\";\nconst youtubeEmbedUrlId = \"TGg4XdImTf4\";\n// The limit of notebooks a user can generate in one conversation\nconst notebookCountLimit = 5;\nvar LanguageEnum = /*#__PURE__*/ function(LanguageEnum) {\n    LanguageEnum[\"English\"] = \"English\";\n    LanguageEnum[\"Spanish\"] = \"Spanish\";\n    LanguageEnum[\"Japanese\"] = \"Japanese\";\n    LanguageEnum[\"Chinese\"] = \"Chinese\";\n    return LanguageEnum;\n}({});\nconst languages = [\n    {\n        value: \"English\",\n        label: \"English\"\n    },\n    {\n        value: \"Spanish\",\n        label: \"Español\"\n    },\n    {\n        value: \"Japanese\",\n        label: \"日本語\"\n    },\n    {\n        value: \"Chinese\",\n        label: \"中文\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/configs/index.ts\n");

/***/ }),

/***/ "(ssr)/./app/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./app/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthContext({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/context/AuthContext.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWtEO0FBTW5DLFNBQVNDLFlBQVksRUFBRUMsUUFBUSxFQUFvQjtJQUNoRSxxQkFBTyw4REFBQ0YsNERBQWVBO2tCQUFFRTs7Ozs7O0FBQzNCIiwic291cmNlcyI6WyIvVXNlcnMvZ2VvZmZyZXlodW5nL0Rlc2t0b3AvZ2l0aHViL25leHQxMy1jbGFyaWZ5L2FwcC9jb250ZXh0L0F1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiO1xuXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhDb250ZXh0UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBdXRoQ29udGV4dCh7IGNoaWxkcmVuIH06IEF1dGhDb250ZXh0UHJvcHMpIHtcbiAgcmV0dXJuIDxTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPjtcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJBdXRoQ29udGV4dCIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/ToasterContext.tsx":
/*!****************************************!*\
  !*** ./app/context/ToasterContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ToasterContext = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/context/ToasterContext.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToasterContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9Ub2FzdGVyQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMEM7QUFFMUMsTUFBTUMsaUJBQWlCO0lBQ3JCLHFCQUFPLDhEQUFDRCxvREFBT0E7Ozs7O0FBQ2pCO0FBRUEsaUVBQWVDLGNBQWNBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9nZW9mZnJleWh1bmcvRGVza3RvcC9naXRodWIvbmV4dDEzLWNsYXJpZnkvYXBwL2NvbnRleHQvVG9hc3RlckNvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSBcInJlYWN0LWhvdC10b2FzdFwiO1xuXG5jb25zdCBUb2FzdGVyQ29udGV4dCA9ICgpID0+IHtcbiAgcmV0dXJuIDxUb2FzdGVyIC8+O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgVG9hc3RlckNvbnRleHQ7XG4iXSwibmFtZXMiOlsiVG9hc3RlciIsIlRvYXN0ZXJDb250ZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/context/ToasterContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/hooks/useLocalStorage.ts":
/*!**************************************!*\
  !*** ./app/hooks/useLocalStorage.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useLocalStorage = (key, initialValue)=>{\n    const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLocalStorage.useEffect\": ()=>{\n            try {\n                // Retrieve from localStorage\n                const item = window.localStorage.getItem(key);\n                if (item) {\n                    // Parse stored json or if none return initialValue\n                    setStoredValue(JSON.parse(item));\n                }\n            } catch (error) {\n                console.error(`Error parsing localStorage key \"${key}\":`, error);\n                // If parsing fails, fall back to the initial value to prevent a crash.\n                setStoredValue(initialValue);\n            }\n        }\n    }[\"useLocalStorage.useEffect\"], [\n        key,\n        initialValue\n    ]);\n    const setValue = (value)=>{\n        try {\n            // Save state\n            setStoredValue(value);\n            // Save to localStorage\n            window.localStorage.setItem(key, JSON.stringify(value));\n        } catch (error) {\n            console.error(`Error setting localStorage key \"${key}\":`, error);\n        }\n    };\n    return [\n        storedValue,\n        setValue\n    ];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useLocalStorage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/hooks/useLocalStorage.ts\n");

/***/ }),

/***/ "(ssr)/./app/libs/posthog.ts":
/*!*****************************!*\
  !*** ./app/libs/posthog.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   identifyUser: () => (/* binding */ identifyUser),\n/* harmony export */   isPostHogReady: () => (/* reexport safe */ _instrumentation_client__WEBPACK_IMPORTED_MODULE_1__.isPostHogReady),\n/* harmony export */   setUserProperties: () => (/* binding */ setUserProperties),\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent)\n/* harmony export */ });\n/* harmony import */ var posthog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! posthog-js */ \"(ssr)/./node_modules/posthog-js/dist/module.js\");\n/* harmony import */ var _instrumentation_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/instrumentation-client */ \"(ssr)/./instrumentation-client.ts\");\n\n\n// Proxy that retries initialization on demand\nconst ensureReady = ()=>{\n    if ((0,_instrumentation_client__WEBPACK_IMPORTED_MODULE_1__.isPostHogReady)()) return true;\n    // Try to (re)initialize – return whatever state we get\n    return (0,_instrumentation_client__WEBPACK_IMPORTED_MODULE_1__.ensurePostHogInitialized)();\n};\n// Utility functions for consistent event tracking\nconst trackEvent = (eventName, properties)=>{\n    if (ensureReady()) {\n        try {\n            posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].capture(eventName, properties);\n        } catch (error) {\n            if (true) {\n                console.error('PostHog trackEvent failed:', error);\n            }\n        }\n    } else if (true) {\n        console.warn('PostHog not initialized, skipping event:', eventName);\n    }\n};\nconst identifyUser = (userId, properties)=>{\n    if (ensureReady()) {\n        try {\n            posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].identify(userId, properties);\n        } catch (error) {\n            if (true) {\n                console.error('PostHog identifyUser failed:', error);\n            }\n        }\n    } else if (true) {\n        console.warn('PostHog not initialized, skipping user identification:', userId);\n    }\n};\nconst setUserProperties = (properties)=>{\n    if (ensureReady()) {\n        try {\n            posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setPersonProperties(properties);\n        } catch (error) {\n            if (true) {\n                console.error('PostHog setUserProperties failed:', error);\n            }\n        }\n    } else if (true) {\n        console.warn('PostHog not initialized, skipping user properties:', properties);\n    }\n};\n// Export the initialization check for external use\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/libs/posthog.ts\n");

/***/ }),

/***/ "(ssr)/./app/polyfills.ts":
/*!**************************!*\
  !*** ./app/polyfills.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// Polyfills for browsers that miss certain modern Web APIs (e.g. Safari < 15.4)\n/**\n * Safari < 15.4 does not implement globalThis.structuredClone.\n * For our usage (deep-cloning plain objects / arrays), a\n * JSON-based fallback is sufficient.\n *\n * NOTE: This fallback will ignore non-serializable values\n * (functions, Map, Set, Date, RegExp, etc.). Our TreeNode\n * objects only contain primitives, so this is safe.\n */ if (typeof globalThis.structuredClone === 'undefined') {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    globalThis.structuredClone = (value)=>JSON.parse(JSON.stringify(value));\n}\n // keep this a module\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcG9seWZpbGxzLnRzIiwibWFwcGluZ3MiOiI7QUFBQSxnRkFBZ0Y7QUFFaEY7Ozs7Ozs7O0NBUUMsR0FDRCxJQUFJLE9BQU9BLFdBQVdDLGVBQWUsS0FBSyxhQUFhO0lBQ3JELDhEQUE4RDtJQUM5REQsV0FBV0MsZUFBZSxHQUFHLENBQUNDLFFBQWVDLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsU0FBUyxDQUFDSDtBQUN6RTtBQUVTLENBQUMscUJBQXFCIiwic291cmNlcyI6WyIvVXNlcnMvZ2VvZmZyZXlodW5nL0Rlc2t0b3AvZ2l0aHViL25leHQxMy1jbGFyaWZ5L2FwcC9wb2x5ZmlsbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUG9seWZpbGxzIGZvciBicm93c2VycyB0aGF0IG1pc3MgY2VydGFpbiBtb2Rlcm4gV2ViIEFQSXMgKGUuZy4gU2FmYXJpIDwgMTUuNClcblxuLyoqXG4gKiBTYWZhcmkgPCAxNS40IGRvZXMgbm90IGltcGxlbWVudCBnbG9iYWxUaGlzLnN0cnVjdHVyZWRDbG9uZS5cbiAqIEZvciBvdXIgdXNhZ2UgKGRlZXAtY2xvbmluZyBwbGFpbiBvYmplY3RzIC8gYXJyYXlzKSwgYVxuICogSlNPTi1iYXNlZCBmYWxsYmFjayBpcyBzdWZmaWNpZW50LlxuICpcbiAqIE5PVEU6IFRoaXMgZmFsbGJhY2sgd2lsbCBpZ25vcmUgbm9uLXNlcmlhbGl6YWJsZSB2YWx1ZXNcbiAqIChmdW5jdGlvbnMsIE1hcCwgU2V0LCBEYXRlLCBSZWdFeHAsIGV0Yy4pLiBPdXIgVHJlZU5vZGVcbiAqIG9iamVjdHMgb25seSBjb250YWluIHByaW1pdGl2ZXMsIHNvIHRoaXMgaXMgc2FmZS5cbiAqL1xuaWYgKHR5cGVvZiBnbG9iYWxUaGlzLnN0cnVjdHVyZWRDbG9uZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgZ2xvYmFsVGhpcy5zdHJ1Y3R1cmVkQ2xvbmUgPSAodmFsdWU6IGFueSkgPT4gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh2YWx1ZSkpXG59XG5cbmV4cG9ydCB7fSAvLyBrZWVwIHRoaXMgYSBtb2R1bGVcbiJdLCJuYW1lcyI6WyJnbG9iYWxUaGlzIiwic3RydWN0dXJlZENsb25lIiwidmFsdWUiLCJKU09OIiwicGFyc2UiLCJzdHJpbmdpZnkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/polyfills.ts\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppContext: () => (/* binding */ AppContext),\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_polyfills__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/polyfills */ \"(ssr)/./app/polyfills.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _vercel_analytics_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vercel/analytics/react */ \"(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\");\n/* harmony import */ var _styles_fonts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/fonts */ \"(ssr)/./styles/fonts.ts\");\n/* harmony import */ var _app_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/hooks/useLocalStorage */ \"(ssr)/./app/hooks/useLocalStorage.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _app_components_PostHogUserIdentifier__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/components/PostHogUserIdentifier */ \"(ssr)/./app/components/PostHogUserIdentifier.tsx\");\n/* harmony import */ var _smastrom_react_rating_style_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smastrom/react-rating/style.css */ \"(ssr)/./node_modules/@smastrom/react-rating/dist/style.css\");\n/* __next_internal_client_entry_do_not_use__ AppContext,default auto */ \n// Polyfill browser APIs for older environments (e.g., Safari <15)\n\n\n\n\n\n\n\n\nconst AppContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    font: 'Default',\n    setFont: ()=>{}\n});\nfunction Providers({ children }) {\n    const [font, setFont] = (0,_app_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_5__[\"default\"])('clarify__font', 'Default');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {\n        value: {\n            font,\n            setFont: setFont\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(_styles_fonts__WEBPACK_IMPORTED_MODULE_4__.displayFontMapper[font], _styles_fonts__WEBPACK_IMPORTED_MODULE_4__.defaultFontMapper[font], 'flex flex-col h-full w-full'),\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/providers.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vercel_analytics_react__WEBPACK_IMPORTED_MODULE_3__.Analytics, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/providers.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_PostHogUserIdentifier__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/providers.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/github/next13-clarify/app/providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./app/users/components/utils.ts":
/*!***************************************!*\
  !*** ./app/users/components/utils.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linksFromSearchResults: () => (/* binding */ linksFromSearchResults),\n/* harmony export */   llmContextFromSearchResults: () => (/* binding */ llmContextFromSearchResults)\n/* harmony export */ });\n// Function to create a formatted string from the resultMap\nconst llmContextFromSearchResults = (searchResults, k)=>{\n    let resultString = \"\";\n    let count = 0; // Counter to ensure we do not exceed k citations\n    for (const res of searchResults){\n        if (count >= k) break;\n        if (!res.scripted_number || !res.extra_snippets) continue;\n        resultString += `${res.scripted_number}:\\n${res.extra_snippets.join(\"\\n\")}\\n\\n`;\n        count++;\n    }\n    return resultString.trim(); // Remove any trailing new lines\n};\nconst linksFromSearchResults = (searchResults, k)=>{\n    let resultString = \"\";\n    let count = 0; // Counter to ensure we do not exceed k citations\n    for (const result of searchResults){\n        if (count >= k) break; // Stop processing if k results have been added\n        if (!result.scripted_number) continue; // Skip results without a scripted number\n        // Append the scripted number, title, and URL to the result string\n        resultString += `${result.scripted_number} ${result.title}\\n${result.url}\\n\\n`;\n        count++;\n    }\n    return resultString;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/users/components/utils.ts\n");

/***/ }),

/***/ "(ssr)/./instrumentation-client.ts":
/*!***********************************!*\
  !*** ./instrumentation-client.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePostHogInitialized: () => (/* binding */ ensurePostHogInitialized),\n/* harmony export */   isPostHogReady: () => (/* binding */ isPostHogReady)\n/* harmony export */ });\n/* harmony import */ var posthog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! posthog-js */ \"(ssr)/./node_modules/posthog-js/dist/module.js\");\n\n// Constant to avoid re-reading env each call\nconst POSTHOG_KEY = \"phc_b5KOcsDhuQm9PF6Tt37RLlgJXsHVanIMq7XZedtD4Io\";\n// Internal state flags\nlet isInitializing = false;\nlet hasAttemptedInit = false;\nlet isLoaded = false;\n/**\n * Returns true when PostHog SDK has completed `on_ready` callback.\n */ const isPostHogReady = ()=>isLoaded;\n/**\n * Attempt to (re)initialize PostHog.\n * Returns current ready state **after** kicking off initialization.\n */ const ensurePostHogInitialized = ()=>{\n    // Prevent SSR, parallel, or repeated attempts\n    if (true) return isLoaded;\n    if (!POSTHOG_KEY) {\n        if ( true && !hasAttemptedInit) {\n            console.warn('PostHog API key missing – set NEXT_PUBLIC_POSTHOG_KEY to enable analytics.');\n        }\n        hasAttemptedInit = true;\n        return false;\n    }\n    isInitializing = true;\n    hasAttemptedInit = true;\n    try {\n        posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(POSTHOG_KEY, {\n            api_host: '/ingest',\n            ui_host: 'https://us.posthog.com',\n            capture_pageview: 'history_change',\n            capture_pageleave: true,\n            capture_exceptions: false,\n            debug: false,\n            // Reduce aggressive autocapture that can trigger aborts\n            autocapture: {\n                dom_event_allowlist: [\n                    'click'\n                ],\n                element_allowlist: [\n                    'a',\n                    'button'\n                ],\n                element_ignorelist: [\n                    'input',\n                    'textarea',\n                    'select'\n                ]\n            },\n            // Persistence settings for better reliability\n            persistence: 'localStorage+cookie',\n            persistence_name: '__posthog',\n            // Reduce network calls in development\n            disable_session_recording: \"development\" === 'development',\n            // Official readiness callback – sets local flag & emits event\n            loaded: ()=>{\n                isLoaded = true;\n                isInitializing = false;\n                if (true) {\n                    // eslint-disable-next-line no-console\n                    console.log('PostHog ready');\n                }\n                window.dispatchEvent(new CustomEvent('posthog-ready'));\n            }\n        });\n    } catch (error) {\n        isInitializing = false;\n        if (true) {\n            console.error('PostHog initialization failed:', error);\n        }\n    }\n    return isLoaded // will still be false until loaded() fires\n    ;\n};\n// Global error handler to suppress PostHog AbortError noise\nif (false) {}\n// Kick-off initialization as early as possible on the client\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./instrumentation-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addSuffixAfterNumber: () => (/* binding */ addSuffixAfterNumber),\n/* harmony export */   calculateMaxActiveConversations: () => (/* binding */ calculateMaxActiveConversations),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   countActiveConversationsAfterDate: () => (/* binding */ countActiveConversationsAfterDate),\n/* harmony export */   formatChatTimestamp: () => (/* binding */ formatChatTimestamp),\n/* harmony export */   getResponseWithSearchContext: () => (/* binding */ getResponseWithSearchContext),\n/* harmony export */   isLocalOrDevEnv: () => (/* binding */ isLocalOrDevEnv),\n/* harmony export */   isUserSubscribing: () => (/* binding */ isUserSubscribing)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.m.js\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/lib/tw-merge.mjs\");\n/* harmony import */ var _app_configs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/configs */ \"(ssr)/./app/configs/index.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_users_components_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/users/components/utils */ \"(ssr)/./app/users/components/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isToday,isYesterday!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isToday,isYesterday!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isToday,isYesterday!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isToday,isYesterday!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/isYesterday/index.js\");\n\n\n\n\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_4__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format timestamp for chat messages with human-readable relative/absolute time\n * @param date - The date to format\n * @returns Formatted timestamp string\n */ function formatChatTimestamp(date) {\n    // Handle null, undefined, or empty values\n    if (!date) {\n        return '';\n    }\n    let dateObj;\n    // Convert string to Date if needed\n    if (typeof date === 'string') {\n        dateObj = new Date(date);\n    } else if (date instanceof Date) {\n        dateObj = date;\n    } else {\n        // Handle any other type by returning empty string\n        return '';\n    }\n    // Handle invalid dates\n    if (isNaN(dateObj.getTime())) {\n        return '';\n    }\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - dateObj.getTime()) / (1000 * 60));\n    // For very recent messages (< 1 minute), show \"Just now\"\n    if (diffInMinutes < 1) {\n        return 'Just now';\n    }\n    // For recent messages (< 1 hour), show relative time like \"2 minutes ago\"\n    if (diffInMinutes < 60) {\n        return (0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(dateObj, {\n            addSuffix: true\n        });\n    }\n    // For today's messages, show \"Today HH:MM AM/PM\"\n    if ((0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(dateObj)) {\n        return `Today ${(0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(dateObj, 'h:mm a')}`;\n    }\n    // For yesterday's messages, show \"Yesterday HH:MM AM/PM\"\n    if ((0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(dateObj)) {\n        return `Yesterday ${(0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(dateObj, 'h:mm a')}`;\n    }\n    // For older messages, show full date and time\n    return (0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(dateObj, 'MMM d, yyyy h:mm a');\n}\n// This is used to add alert after number to remind users that LLM often BS\n// eg: if suffix is (EXAMPLE NUMBER)\n// 1. **Standard Numbers**\n//    - **Input:** \"The population of the city is 3,250.\"\n//    - **Output:** \"The population of the city is 3,250 (EXAMPLE NUMBER).\"\n// 2. **Numbers with Commas (thousands separators)**\n//    - **Input:** \"The company's revenue reached $2,000,000 last year.\"\n//    - **Output:** \"The company's revenue reached $2,000,000 (EXAMPLE NUMBER) last year.\"\n// 3. **Numbers with Decimals**\n//    - **Input:** \"The area of the office is 250.75 square meters.\"\n//    - **Output:** \"The area of the office is 250.75 (EXAMPLE NUMBER) square meters.\"\n// 4. **Percentages**\n//    - **Input:** \"About 15% of the software installed is outdated.\"\n//    - **Output:** \"About 15% (EXAMPLE NUMBER) of the software installed is outdated.\"\n// 5. **Ranges with Hyphens**\n//    - **Input:** \"The expected attendees are 1,000-1,500 people.\"\n//    - **Output:** \"The expected attendees are 1,000-1,500 (EXAMPLE NUMBER) people.\"\n// 6. **Numbers Followed by Punctuation**\n//     - **Input:** \"The project requires an investment of $1,000,000; xxx\"\n//     - **Output:** \"The project requires an investment of $1,000,000 (EXAMPLE NUMBER); xxx\"\nfunction addSuffixAfterNumber(inputText, suffix) {\n    // Updated regex to handle numbers including commas, periods, percent signs, exclamation marks, and hyphens\n    const regex = /(\\d[\\d,.%!-]*)(\\s|\\b)/g;\n    // Replace by inserting the suffix right after the complete number and before the space or word boundary\n    return inputText.replace(regex, `$1 ${suffix}$2`);\n}\nfunction countActiveConversationsAfterDate(conversationList, status, date) {\n    return conversationList.filter((item)=>{\n        const isActive = item.conversation_status === status;\n        const isAfterDate = new Date(item.created_at) > new Date(date);\n        return isActive && isAfterDate;\n    }).length || 0;\n}\nconst isLocalOrDevEnv = ()=>{\n    // Localhost or dev environment\n    if (false) {}\n    return false;\n};\nconst isUserSubscribing = (user)=>{\n    if (user === null) {\n        return false;\n    }\n    const isGuest = user.subscription_tier === _prisma_client__WEBPACK_IMPORTED_MODULE_2__.SubscriptionTier.GUEST;\n    const hasValidSubscription = Boolean(user.subscription_end_date && new Date(user.subscription_end_date) > new Date());\n    return isGuest || hasValidSubscription;\n};\nfunction calculateMaxActiveConversations(user) {\n    if (isUserSubscribing(user)) {\n        return 42;\n    } else if (isLocalOrDevEnv()) {\n        return 30;\n    } else {\n        return _app_configs__WEBPACK_IMPORTED_MODULE_1__.maxActiveConversations;\n    }\n}\nconst getResponseWithSearchContext = (response, searchResults, numberOfLinksToUse)=>{\n    // If there's no response, return an empty string\n    if (!response) {\n        return '';\n    }\n    // If there's no search results, return the response\n    if (searchResults === null || searchResults.length === 0) {\n        return response;\n    }\n    const contextLinks = (0,_app_users_components_utils__WEBPACK_IMPORTED_MODULE_3__.linksFromSearchResults)(searchResults, numberOfLinksToUse);\n    return `${response}\\n\\n=== Context sources: ===\\n${contextLinks}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ0o7QUFDYztBQUNMO0FBQ29CO0FBR087QUFFckUsU0FBU1MsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPVCx1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNVO0FBQ3RCO0FBRUE7Ozs7Q0FJQyxHQUNNLFNBQVNDLG9CQUNkQyxJQUFzQztJQUV0QywwQ0FBMEM7SUFDMUMsSUFBSSxDQUFDQSxNQUFNO1FBQ1QsT0FBTztJQUNUO0lBRUEsSUFBSUM7SUFFSixtQ0FBbUM7SUFDbkMsSUFBSSxPQUFPRCxTQUFTLFVBQVU7UUFDNUJDLFVBQVUsSUFBSUMsS0FBS0Y7SUFDckIsT0FBTyxJQUFJQSxnQkFBZ0JFLE1BQU07UUFDL0JELFVBQVVEO0lBQ1osT0FBTztRQUNMLGtEQUFrRDtRQUNsRCxPQUFPO0lBQ1Q7SUFFQSx1QkFBdUI7SUFDdkIsSUFBSUcsTUFBTUYsUUFBUUcsT0FBTyxLQUFLO1FBQzVCLE9BQU87SUFDVDtJQUVBLE1BQU1DLE1BQU0sSUFBSUg7SUFDaEIsTUFBTUksZ0JBQWdCQyxLQUFLQyxLQUFLLENBQzlCLENBQUNILElBQUlELE9BQU8sS0FBS0gsUUFBUUcsT0FBTyxFQUFDLElBQU0sUUFBTyxFQUFDO0lBR2pELHlEQUF5RDtJQUN6RCxJQUFJRSxnQkFBZ0IsR0FBRztRQUNyQixPQUFPO0lBQ1Q7SUFFQSwwRUFBMEU7SUFDMUUsSUFBSUEsZ0JBQWdCLElBQUk7UUFDdEIsT0FBT2IsMEhBQW1CQSxDQUFDUSxTQUFTO1lBQUVRLFdBQVc7UUFBSztJQUN4RDtJQUVBLGlEQUFpRDtJQUNqRCxJQUFJZCwwSEFBT0EsQ0FBQ00sVUFBVTtRQUNwQixPQUFPLENBQUMsTUFBTSxFQUFFUCwwSEFBTUEsQ0FBQ08sU0FBUyxXQUFXO0lBQzdDO0lBRUEseURBQXlEO0lBQ3pELElBQUlMLDBIQUFXQSxDQUFDSyxVQUFVO1FBQ3hCLE9BQU8sQ0FBQyxVQUFVLEVBQUVQLDBIQUFNQSxDQUFDTyxTQUFTLFdBQVc7SUFDakQ7SUFFQSw4Q0FBOEM7SUFDOUMsT0FBT1AsMEhBQU1BLENBQUNPLFNBQVM7QUFDekI7QUFFQSwyRUFBMkU7QUFDM0Usb0NBQW9DO0FBQ3BDLDBCQUEwQjtBQUMxQix5REFBeUQ7QUFDekQsMkVBQTJFO0FBRTNFLG9EQUFvRDtBQUNwRCx3RUFBd0U7QUFDeEUsMEZBQTBGO0FBRTFGLCtCQUErQjtBQUMvQixvRUFBb0U7QUFDcEUsc0ZBQXNGO0FBRXRGLHFCQUFxQjtBQUNyQixxRUFBcUU7QUFDckUsdUZBQXVGO0FBRXZGLDZCQUE2QjtBQUM3QixtRUFBbUU7QUFDbkUscUZBQXFGO0FBRXJGLHlDQUF5QztBQUN6QywyRUFBMkU7QUFDM0UsNkZBQTZGO0FBRXRGLFNBQVNTLHFCQUNkQyxTQUFpQixFQUNqQkMsTUFBYztJQUVkLDJHQUEyRztJQUMzRyxNQUFNQyxRQUFRO0lBQ2Qsd0dBQXdHO0lBQ3hHLE9BQU9GLFVBQVVHLE9BQU8sQ0FBQ0QsT0FBTyxDQUFDLEdBQUcsRUFBRUQsT0FBTyxFQUFFLENBQUM7QUFDbEQ7QUFFTyxTQUFTRyxrQ0FDZEMsZ0JBQTBDLEVBQzFDQyxNQUEwQixFQUMxQmpCLElBQVU7SUFFVixPQUNFZ0IsaUJBQWlCRSxNQUFNLENBQUNDLENBQUFBO1FBQ3RCLE1BQU1DLFdBQVdELEtBQUtFLG1CQUFtQixLQUFLSjtRQUM5QyxNQUFNSyxjQUFjLElBQUlwQixLQUFLaUIsS0FBS0ksVUFBVSxJQUFJLElBQUlyQixLQUFLRjtRQUV6RCxPQUFPb0IsWUFBWUU7SUFDckIsR0FBR0UsTUFBTSxJQUFJO0FBRWpCO0FBRU8sTUFBTUMsa0JBQWtCO0lBQzdCLCtCQUErQjtJQUMvQixJQUFJLEtBQTZCLEVBQUUsRUFLbEM7SUFDRCxPQUFPO0FBQ1QsRUFBQztBQUVNLE1BQU1LLG9CQUFvQixDQUFDQztJQUNoQyxJQUFJQSxTQUFTLE1BQU07UUFDakIsT0FBTztJQUNUO0lBRUEsTUFBTUMsVUFBVUQsS0FBS0UsaUJBQWlCLEtBQUsxQyw0REFBZ0JBLENBQUMyQyxLQUFLO0lBQ2pFLE1BQU1DLHVCQUF1QkMsUUFDM0JMLEtBQUtNLHFCQUFxQixJQUN4QixJQUFJbkMsS0FBSzZCLEtBQUtNLHFCQUFxQixJQUFJLElBQUluQztJQUcvQyxPQUFPOEIsV0FBV0c7QUFDcEIsRUFBQztBQUVNLFNBQVNHLGdDQUFnQ1AsSUFBaUI7SUFDL0QsSUFBSUQsa0JBQWtCQyxPQUFPO1FBQzNCLE9BQU87SUFDVCxPQUFPLElBQUlOLG1CQUFtQjtRQUM1QixPQUFPO0lBQ1QsT0FBTztRQUNMLE9BQU9uQyxnRUFBc0JBO0lBQy9CO0FBQ0Y7QUFFTyxNQUFNaUQsK0JBQStCLENBQzFDQyxVQUNBQyxlQUNBQztJQUVBLGlEQUFpRDtJQUNqRCxJQUFJLENBQUNGLFVBQVU7UUFDYixPQUFPO0lBQ1Q7SUFDQSxvREFBb0Q7SUFDcEQsSUFBSUMsa0JBQWtCLFFBQVFBLGNBQWNqQixNQUFNLEtBQUssR0FBRztRQUN4RCxPQUFPZ0I7SUFDVDtJQUVBLE1BQU1HLGVBQWVuRCxtRkFBc0JBLENBQUNpRCxlQUFlQztJQUUzRCxPQUFPLEdBQUdGLFNBQVMsOEJBQThCLEVBQUVHLGNBQWM7QUFDbkUsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2dlb2ZmcmV5aHVuZy9EZXNrdG9wL2dpdGh1Yi9uZXh0MTMtY2xhcmlmeS9saWIvdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29udmVyc2F0aW9uU3RhdHVzLCBVc2VyIH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5pbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tICdjbHN4J1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJ1xuaW1wb3J0IHsgbWF4QWN0aXZlQ29udmVyc2F0aW9ucyB9IGZyb20gJ0AvYXBwL2NvbmZpZ3MnXG5pbXBvcnQgeyBTdWJzY3JpcHRpb25UaWVyIH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5pbXBvcnQgeyBsaW5rc0Zyb21TZWFyY2hSZXN1bHRzIH0gZnJvbSAnQC9hcHAvdXNlcnMvY29tcG9uZW50cy91dGlscydcbmltcG9ydCB7IFNlYXJjaFJlc3VsdCB9IGZyb20gJ0AvYXBwL2FwaS9yYWcvdXRpbHMnXG5pbXBvcnQgeyBDb252ZXJzYXRpb25MYXlvdXRUeXBlIH0gZnJvbSAnQC9hcHAvdHlwZXMnXG5pbXBvcnQgeyBmb3JtYXREaXN0YW5jZVRvTm93LCBmb3JtYXQsIGlzVG9kYXksIGlzWWVzdGVyZGF5IH0gZnJvbSAnZGF0ZS1mbnMnXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbi8qKlxuICogRm9ybWF0IHRpbWVzdGFtcCBmb3IgY2hhdCBtZXNzYWdlcyB3aXRoIGh1bWFuLXJlYWRhYmxlIHJlbGF0aXZlL2Fic29sdXRlIHRpbWVcbiAqIEBwYXJhbSBkYXRlIC0gVGhlIGRhdGUgdG8gZm9ybWF0XG4gKiBAcmV0dXJucyBGb3JtYXR0ZWQgdGltZXN0YW1wIHN0cmluZ1xuICovXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0Q2hhdFRpbWVzdGFtcChcbiAgZGF0ZTogRGF0ZSB8IHN0cmluZyB8IG51bGwgfCB1bmRlZmluZWRcbik6IHN0cmluZyB7XG4gIC8vIEhhbmRsZSBudWxsLCB1bmRlZmluZWQsIG9yIGVtcHR5IHZhbHVlc1xuICBpZiAoIWRhdGUpIHtcbiAgICByZXR1cm4gJydcbiAgfVxuXG4gIGxldCBkYXRlT2JqOiBEYXRlXG5cbiAgLy8gQ29udmVydCBzdHJpbmcgdG8gRGF0ZSBpZiBuZWVkZWRcbiAgaWYgKHR5cGVvZiBkYXRlID09PSAnc3RyaW5nJykge1xuICAgIGRhdGVPYmogPSBuZXcgRGF0ZShkYXRlKVxuICB9IGVsc2UgaWYgKGRhdGUgaW5zdGFuY2VvZiBEYXRlKSB7XG4gICAgZGF0ZU9iaiA9IGRhdGVcbiAgfSBlbHNlIHtcbiAgICAvLyBIYW5kbGUgYW55IG90aGVyIHR5cGUgYnkgcmV0dXJuaW5nIGVtcHR5IHN0cmluZ1xuICAgIHJldHVybiAnJ1xuICB9XG5cbiAgLy8gSGFuZGxlIGludmFsaWQgZGF0ZXNcbiAgaWYgKGlzTmFOKGRhdGVPYmouZ2V0VGltZSgpKSkge1xuICAgIHJldHVybiAnJ1xuICB9XG5cbiAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxuICBjb25zdCBkaWZmSW5NaW51dGVzID0gTWF0aC5mbG9vcihcbiAgICAobm93LmdldFRpbWUoKSAtIGRhdGVPYmouZ2V0VGltZSgpKSAvICgxMDAwICogNjApXG4gIClcblxuICAvLyBGb3IgdmVyeSByZWNlbnQgbWVzc2FnZXMgKDwgMSBtaW51dGUpLCBzaG93IFwiSnVzdCBub3dcIlxuICBpZiAoZGlmZkluTWludXRlcyA8IDEpIHtcbiAgICByZXR1cm4gJ0p1c3Qgbm93J1xuICB9XG5cbiAgLy8gRm9yIHJlY2VudCBtZXNzYWdlcyAoPCAxIGhvdXIpLCBzaG93IHJlbGF0aXZlIHRpbWUgbGlrZSBcIjIgbWludXRlcyBhZ29cIlxuICBpZiAoZGlmZkluTWludXRlcyA8IDYwKSB7XG4gICAgcmV0dXJuIGZvcm1hdERpc3RhbmNlVG9Ob3coZGF0ZU9iaiwgeyBhZGRTdWZmaXg6IHRydWUgfSlcbiAgfVxuXG4gIC8vIEZvciB0b2RheSdzIG1lc3NhZ2VzLCBzaG93IFwiVG9kYXkgSEg6TU0gQU0vUE1cIlxuICBpZiAoaXNUb2RheShkYXRlT2JqKSkge1xuICAgIHJldHVybiBgVG9kYXkgJHtmb3JtYXQoZGF0ZU9iaiwgJ2g6bW0gYScpfWBcbiAgfVxuXG4gIC8vIEZvciB5ZXN0ZXJkYXkncyBtZXNzYWdlcywgc2hvdyBcIlllc3RlcmRheSBISDpNTSBBTS9QTVwiXG4gIGlmIChpc1llc3RlcmRheShkYXRlT2JqKSkge1xuICAgIHJldHVybiBgWWVzdGVyZGF5ICR7Zm9ybWF0KGRhdGVPYmosICdoOm1tIGEnKX1gXG4gIH1cblxuICAvLyBGb3Igb2xkZXIgbWVzc2FnZXMsIHNob3cgZnVsbCBkYXRlIGFuZCB0aW1lXG4gIHJldHVybiBmb3JtYXQoZGF0ZU9iaiwgJ01NTSBkLCB5eXl5IGg6bW0gYScpXG59XG5cbi8vIFRoaXMgaXMgdXNlZCB0byBhZGQgYWxlcnQgYWZ0ZXIgbnVtYmVyIHRvIHJlbWluZCB1c2VycyB0aGF0IExMTSBvZnRlbiBCU1xuLy8gZWc6IGlmIHN1ZmZpeCBpcyAoRVhBTVBMRSBOVU1CRVIpXG4vLyAxLiAqKlN0YW5kYXJkIE51bWJlcnMqKlxuLy8gICAgLSAqKklucHV0OioqIFwiVGhlIHBvcHVsYXRpb24gb2YgdGhlIGNpdHkgaXMgMywyNTAuXCJcbi8vICAgIC0gKipPdXRwdXQ6KiogXCJUaGUgcG9wdWxhdGlvbiBvZiB0aGUgY2l0eSBpcyAzLDI1MCAoRVhBTVBMRSBOVU1CRVIpLlwiXG5cbi8vIDIuICoqTnVtYmVycyB3aXRoIENvbW1hcyAodGhvdXNhbmRzIHNlcGFyYXRvcnMpKipcbi8vICAgIC0gKipJbnB1dDoqKiBcIlRoZSBjb21wYW55J3MgcmV2ZW51ZSByZWFjaGVkICQyLDAwMCwwMDAgbGFzdCB5ZWFyLlwiXG4vLyAgICAtICoqT3V0cHV0OioqIFwiVGhlIGNvbXBhbnkncyByZXZlbnVlIHJlYWNoZWQgJDIsMDAwLDAwMCAoRVhBTVBMRSBOVU1CRVIpIGxhc3QgeWVhci5cIlxuXG4vLyAzLiAqKk51bWJlcnMgd2l0aCBEZWNpbWFscyoqXG4vLyAgICAtICoqSW5wdXQ6KiogXCJUaGUgYXJlYSBvZiB0aGUgb2ZmaWNlIGlzIDI1MC43NSBzcXVhcmUgbWV0ZXJzLlwiXG4vLyAgICAtICoqT3V0cHV0OioqIFwiVGhlIGFyZWEgb2YgdGhlIG9mZmljZSBpcyAyNTAuNzUgKEVYQU1QTEUgTlVNQkVSKSBzcXVhcmUgbWV0ZXJzLlwiXG5cbi8vIDQuICoqUGVyY2VudGFnZXMqKlxuLy8gICAgLSAqKklucHV0OioqIFwiQWJvdXQgMTUlIG9mIHRoZSBzb2Z0d2FyZSBpbnN0YWxsZWQgaXMgb3V0ZGF0ZWQuXCJcbi8vICAgIC0gKipPdXRwdXQ6KiogXCJBYm91dCAxNSUgKEVYQU1QTEUgTlVNQkVSKSBvZiB0aGUgc29mdHdhcmUgaW5zdGFsbGVkIGlzIG91dGRhdGVkLlwiXG5cbi8vIDUuICoqUmFuZ2VzIHdpdGggSHlwaGVucyoqXG4vLyAgICAtICoqSW5wdXQ6KiogXCJUaGUgZXhwZWN0ZWQgYXR0ZW5kZWVzIGFyZSAxLDAwMC0xLDUwMCBwZW9wbGUuXCJcbi8vICAgIC0gKipPdXRwdXQ6KiogXCJUaGUgZXhwZWN0ZWQgYXR0ZW5kZWVzIGFyZSAxLDAwMC0xLDUwMCAoRVhBTVBMRSBOVU1CRVIpIHBlb3BsZS5cIlxuXG4vLyA2LiAqKk51bWJlcnMgRm9sbG93ZWQgYnkgUHVuY3R1YXRpb24qKlxuLy8gICAgIC0gKipJbnB1dDoqKiBcIlRoZSBwcm9qZWN0IHJlcXVpcmVzIGFuIGludmVzdG1lbnQgb2YgJDEsMDAwLDAwMDsgeHh4XCJcbi8vICAgICAtICoqT3V0cHV0OioqIFwiVGhlIHByb2plY3QgcmVxdWlyZXMgYW4gaW52ZXN0bWVudCBvZiAkMSwwMDAsMDAwIChFWEFNUExFIE5VTUJFUik7IHh4eFwiXG5cbmV4cG9ydCBmdW5jdGlvbiBhZGRTdWZmaXhBZnRlck51bWJlcihcbiAgaW5wdXRUZXh0OiBzdHJpbmcsXG4gIHN1ZmZpeDogc3RyaW5nXG4pOiBzdHJpbmcge1xuICAvLyBVcGRhdGVkIHJlZ2V4IHRvIGhhbmRsZSBudW1iZXJzIGluY2x1ZGluZyBjb21tYXMsIHBlcmlvZHMsIHBlcmNlbnQgc2lnbnMsIGV4Y2xhbWF0aW9uIG1hcmtzLCBhbmQgaHlwaGVuc1xuICBjb25zdCByZWdleCA9IC8oXFxkW1xcZCwuJSEtXSopKFxcc3xcXGIpL2dcbiAgLy8gUmVwbGFjZSBieSBpbnNlcnRpbmcgdGhlIHN1ZmZpeCByaWdodCBhZnRlciB0aGUgY29tcGxldGUgbnVtYmVyIGFuZCBiZWZvcmUgdGhlIHNwYWNlIG9yIHdvcmQgYm91bmRhcnlcbiAgcmV0dXJuIGlucHV0VGV4dC5yZXBsYWNlKHJlZ2V4LCBgJDEgJHtzdWZmaXh9JDJgKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gY291bnRBY3RpdmVDb252ZXJzYXRpb25zQWZ0ZXJEYXRlKFxuICBjb252ZXJzYXRpb25MaXN0OiBDb252ZXJzYXRpb25MYXlvdXRUeXBlW10sXG4gIHN0YXR1czogQ29udmVyc2F0aW9uU3RhdHVzLFxuICBkYXRlOiBEYXRlXG4pOiBudW1iZXIge1xuICByZXR1cm4gKFxuICAgIGNvbnZlcnNhdGlvbkxpc3QuZmlsdGVyKGl0ZW0gPT4ge1xuICAgICAgY29uc3QgaXNBY3RpdmUgPSBpdGVtLmNvbnZlcnNhdGlvbl9zdGF0dXMgPT09IHN0YXR1c1xuICAgICAgY29uc3QgaXNBZnRlckRhdGUgPSBuZXcgRGF0ZShpdGVtLmNyZWF0ZWRfYXQpID4gbmV3IERhdGUoZGF0ZSlcblxuICAgICAgcmV0dXJuIGlzQWN0aXZlICYmIGlzQWZ0ZXJEYXRlXG4gICAgfSkubGVuZ3RoIHx8IDBcbiAgKVxufVxuXG5leHBvcnQgY29uc3QgaXNMb2NhbE9yRGV2RW52ID0gKCkgPT4ge1xuICAvLyBMb2NhbGhvc3Qgb3IgZGV2IGVudmlyb25tZW50XG4gIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgIHJldHVybiAoXG4gICAgICB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUgPT09ICdsb2NhbGhvc3QnIHx8XG4gICAgICB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ2RldicpXG4gICAgKVxuICB9XG4gIHJldHVybiBmYWxzZVxufVxuXG5leHBvcnQgY29uc3QgaXNVc2VyU3Vic2NyaWJpbmcgPSAodXNlcjogVXNlciB8IG51bGwpOiBib29sZWFuID0+IHtcbiAgaWYgKHVzZXIgPT09IG51bGwpIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIGNvbnN0IGlzR3Vlc3QgPSB1c2VyLnN1YnNjcmlwdGlvbl90aWVyID09PSBTdWJzY3JpcHRpb25UaWVyLkdVRVNUXG4gIGNvbnN0IGhhc1ZhbGlkU3Vic2NyaXB0aW9uID0gQm9vbGVhbihcbiAgICB1c2VyLnN1YnNjcmlwdGlvbl9lbmRfZGF0ZSAmJlxuICAgICAgbmV3IERhdGUodXNlci5zdWJzY3JpcHRpb25fZW5kX2RhdGUpID4gbmV3IERhdGUoKVxuICApXG5cbiAgcmV0dXJuIGlzR3Vlc3QgfHwgaGFzVmFsaWRTdWJzY3JpcHRpb25cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNhbGN1bGF0ZU1heEFjdGl2ZUNvbnZlcnNhdGlvbnModXNlcjogVXNlciB8IG51bGwpOiBudW1iZXIge1xuICBpZiAoaXNVc2VyU3Vic2NyaWJpbmcodXNlcikpIHtcbiAgICByZXR1cm4gNDJcbiAgfSBlbHNlIGlmIChpc0xvY2FsT3JEZXZFbnYoKSkge1xuICAgIHJldHVybiAzMFxuICB9IGVsc2Uge1xuICAgIHJldHVybiBtYXhBY3RpdmVDb252ZXJzYXRpb25zXG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IGdldFJlc3BvbnNlV2l0aFNlYXJjaENvbnRleHQgPSAoXG4gIHJlc3BvbnNlOiBzdHJpbmcsXG4gIHNlYXJjaFJlc3VsdHM6IFNlYXJjaFJlc3VsdFtdIHwgbnVsbCxcbiAgbnVtYmVyT2ZMaW5rc1RvVXNlOiBudW1iZXJcbik6IHN0cmluZyA9PiB7XG4gIC8vIElmIHRoZXJlJ3Mgbm8gcmVzcG9uc2UsIHJldHVybiBhbiBlbXB0eSBzdHJpbmdcbiAgaWYgKCFyZXNwb25zZSkge1xuICAgIHJldHVybiAnJ1xuICB9XG4gIC8vIElmIHRoZXJlJ3Mgbm8gc2VhcmNoIHJlc3VsdHMsIHJldHVybiB0aGUgcmVzcG9uc2VcbiAgaWYgKHNlYXJjaFJlc3VsdHMgPT09IG51bGwgfHwgc2VhcmNoUmVzdWx0cy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gcmVzcG9uc2VcbiAgfVxuXG4gIGNvbnN0IGNvbnRleHRMaW5rcyA9IGxpbmtzRnJvbVNlYXJjaFJlc3VsdHMoc2VhcmNoUmVzdWx0cywgbnVtYmVyT2ZMaW5rc1RvVXNlKVxuXG4gIHJldHVybiBgJHtyZXNwb25zZX1cXG5cXG49PT0gQ29udGV4dCBzb3VyY2VzOiA9PT1cXG4ke2NvbnRleHRMaW5rc31gXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJtYXhBY3RpdmVDb252ZXJzYXRpb25zIiwiU3Vic2NyaXB0aW9uVGllciIsImxpbmtzRnJvbVNlYXJjaFJlc3VsdHMiLCJmb3JtYXREaXN0YW5jZVRvTm93IiwiZm9ybWF0IiwiaXNUb2RheSIsImlzWWVzdGVyZGF5IiwiY24iLCJpbnB1dHMiLCJmb3JtYXRDaGF0VGltZXN0YW1wIiwiZGF0ZSIsImRhdGVPYmoiLCJEYXRlIiwiaXNOYU4iLCJnZXRUaW1lIiwibm93IiwiZGlmZkluTWludXRlcyIsIk1hdGgiLCJmbG9vciIsImFkZFN1ZmZpeCIsImFkZFN1ZmZpeEFmdGVyTnVtYmVyIiwiaW5wdXRUZXh0Iiwic3VmZml4IiwicmVnZXgiLCJyZXBsYWNlIiwiY291bnRBY3RpdmVDb252ZXJzYXRpb25zQWZ0ZXJEYXRlIiwiY29udmVyc2F0aW9uTGlzdCIsInN0YXR1cyIsImZpbHRlciIsIml0ZW0iLCJpc0FjdGl2ZSIsImNvbnZlcnNhdGlvbl9zdGF0dXMiLCJpc0FmdGVyRGF0ZSIsImNyZWF0ZWRfYXQiLCJsZW5ndGgiLCJpc0xvY2FsT3JEZXZFbnYiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhvc3RuYW1lIiwiaW5jbHVkZXMiLCJpc1VzZXJTdWJzY3JpYmluZyIsInVzZXIiLCJpc0d1ZXN0Iiwic3Vic2NyaXB0aW9uX3RpZXIiLCJHVUVTVCIsImhhc1ZhbGlkU3Vic2NyaXB0aW9uIiwiQm9vbGVhbiIsInN1YnNjcmlwdGlvbl9lbmRfZGF0ZSIsImNhbGN1bGF0ZU1heEFjdGl2ZUNvbnZlcnNhdGlvbnMiLCJnZXRSZXNwb25zZVdpdGhTZWFyY2hDb250ZXh0IiwicmVzcG9uc2UiLCJzZWFyY2hSZXN1bHRzIiwibnVtYmVyT2ZMaW5rc1RvVXNlIiwiY29udGV4dExpbmtzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FToasterContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2F%40vercel%2Fanalytics%2Fdist%2Freact%2Findex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fstyles%2Fprosemirror.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FToasterContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2F%40vercel%2Fanalytics%2Fdist%2Freact%2Findex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fstyles%2Fprosemirror.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/AuthContext.tsx */ \"(ssr)/./app/context/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/ToasterContext.tsx */ \"(ssr)/./app/context/ToasterContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@vercel/analytics/dist/react/index.mjs */ \"(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fcontext%2FToasterContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp%2Fproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2F%40vercel%2Fanalytics%2Fdist%2Freact%2Findex.mjs%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fstyles%2Fprosemirror.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./styles/fonts.ts":
/*!*************************!*\
  !*** ./styles/fonts.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cal: () => (/* reexport default from dynamic */ next_font_local_target_css_path_styles_fonts_ts_import_arguments_src_CalSans_SemiBold_otf_variable_font_display_variableName_cal___WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   crimson: () => (/* reexport default from dynamic */ next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_400_variable_font_default_subsets_latin_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default.a),\n/* harmony export */   crimsonBold: () => (/* reexport default from dynamic */ next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_700_variable_font_display_subsets_latin_variableName_crimsonBold___WEBPACK_IMPORTED_MODULE_1___default.a),\n/* harmony export */   defaultFontMapper: () => (/* binding */ defaultFontMapper),\n/* harmony export */   displayFontMapper: () => (/* binding */ displayFontMapper),\n/* harmony export */   inconsolata: () => (/* reexport default from dynamic */ next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_variable_font_default_subsets_latin_variableName_inconsolata___WEBPACK_IMPORTED_MODULE_5___default.a),\n/* harmony export */   inconsolataBold: () => (/* reexport default from dynamic */ next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_weight_700_variable_font_display_subsets_latin_variableName_inconsolataBold___WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   inter: () => (/* reexport default from dynamic */ next_font_google_target_css_path_styles_fonts_ts_import_Inter_arguments_variable_font_default_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default.a)\n/* harmony export */ });\n/* harmony import */ var next_font_local_target_css_path_styles_fonts_ts_import_arguments_src_CalSans_SemiBold_otf_variable_font_display_variableName_cal___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"styles/fonts.ts\",\"import\":\"\",\"arguments\":[{\"src\":\"./CalSans-SemiBold.otf\",\"variable\":\"--font-display\"}],\"variableName\":\"cal\"} */ \"(ssr)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"styles/fonts.ts\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./CalSans-SemiBold.otf\\\",\\\"variable\\\":\\\"--font-display\\\"}],\\\"variableName\\\":\\\"cal\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_styles_fonts_ts_import_arguments_src_CalSans_SemiBold_otf_variable_font_display_variableName_cal___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_styles_fonts_ts_import_arguments_src_CalSans_SemiBold_otf_variable_font_display_variableName_cal___WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_700_variable_font_display_subsets_latin_variableName_crimsonBold___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"styles/fonts.ts\",\"import\":\"Crimson_Text\",\"arguments\":[{\"weight\":\"700\",\"variable\":\"--font-display\",\"subsets\":[\"latin\"]}],\"variableName\":\"crimsonBold\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"styles/fonts.ts\\\",\\\"import\\\":\\\"Crimson_Text\\\",\\\"arguments\\\":[{\\\"weight\\\":\\\"700\\\",\\\"variable\\\":\\\"--font-display\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"crimsonBold\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_700_variable_font_display_subsets_latin_variableName_crimsonBold___WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_700_variable_font_display_subsets_latin_variableName_crimsonBold___WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Inter_arguments_variable_font_default_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"styles/fonts.ts\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-default\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"styles/fonts.ts\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-default\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Inter_arguments_variable_font_default_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_styles_fonts_ts_import_Inter_arguments_variable_font_default_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_weight_700_variable_font_display_subsets_latin_variableName_inconsolataBold___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"styles/fonts.ts\",\"import\":\"Inconsolata\",\"arguments\":[{\"weight\":\"700\",\"variable\":\"--font-display\",\"subsets\":[\"latin\"]}],\"variableName\":\"inconsolataBold\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"styles/fonts.ts\\\",\\\"import\\\":\\\"Inconsolata\\\",\\\"arguments\\\":[{\\\"weight\\\":\\\"700\\\",\\\"variable\\\":\\\"--font-display\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inconsolataBold\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_weight_700_variable_font_display_subsets_latin_variableName_inconsolataBold___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_weight_700_variable_font_display_subsets_latin_variableName_inconsolataBold___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_400_variable_font_default_subsets_latin_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"styles/fonts.ts\",\"import\":\"Crimson_Text\",\"arguments\":[{\"weight\":\"400\",\"variable\":\"--font-default\",\"subsets\":[\"latin\"]}],\"variableName\":\"crimson\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"styles/fonts.ts\\\",\\\"import\\\":\\\"Crimson_Text\\\",\\\"arguments\\\":[{\\\"weight\\\":\\\"400\\\",\\\"variable\\\":\\\"--font-default\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"crimson\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_400_variable_font_default_subsets_latin_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_400_variable_font_default_subsets_latin_variableName_crimson___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_variable_font_default_subsets_latin_variableName_inconsolata___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"styles/fonts.ts\",\"import\":\"Inconsolata\",\"arguments\":[{\"variable\":\"--font-default\",\"subsets\":[\"latin\"]}],\"variableName\":\"inconsolata\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"styles/fonts.ts\\\",\\\"import\\\":\\\"Inconsolata\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-default\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inconsolata\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_variable_font_default_subsets_latin_variableName_inconsolata___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_variable_font_default_subsets_latin_variableName_inconsolata___WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nconst displayFontMapper = {\n    Default: (next_font_local_target_css_path_styles_fonts_ts_import_arguments_src_CalSans_SemiBold_otf_variable_font_display_variableName_cal___WEBPACK_IMPORTED_MODULE_0___default().variable),\n    Serif: (next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_700_variable_font_display_subsets_latin_variableName_crimsonBold___WEBPACK_IMPORTED_MODULE_1___default().variable),\n    Mono: (next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_weight_700_variable_font_display_subsets_latin_variableName_inconsolataBold___WEBPACK_IMPORTED_MODULE_2___default().variable)\n};\nconst defaultFontMapper = {\n    Default: (next_font_google_target_css_path_styles_fonts_ts_import_Inter_arguments_variable_font_default_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable),\n    Serif: (next_font_google_target_css_path_styles_fonts_ts_import_Crimson_Text_arguments_weight_400_variable_font_default_subsets_latin_variableName_crimson___WEBPACK_IMPORTED_MODULE_4___default().variable),\n    Mono: (next_font_google_target_css_path_styles_fonts_ts_import_Inconsolata_arguments_variable_font_default_subsets_latin_variableName_inconsolata___WEBPACK_IMPORTED_MODULE_5___default().variable)\n};\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./styles/fonts.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@opentelemetry","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/posthog-js","vendor-chunks/date-fns","vendor-chunks/tailwind-merge","vendor-chunks/react-hot-toast","vendor-chunks/@vercel","vendor-chunks/goober","vendor-chunks/clsx","vendor-chunks/@smastrom"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgeoffreyhung%2FDesktop%2Fgithub%2Fnext13-clarify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();