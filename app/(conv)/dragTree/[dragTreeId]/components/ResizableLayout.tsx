'use client'

import React, { useState, useRef } from 'react'
import { cn } from '@/lib/utils'
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@/components/ui/resizable'
import type { ImperativePanelHandle } from 'react-resizable-panels'
import { useIsMobile } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useMediaQuery'
import MobileTabNavigation, {
  type MobileTab,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/MobileTabNavigation'
import MobileRecommendationBanner from '@/app/(conv)/dragTree/[dragTreeId]/components/MobileRecommendationBanner'
import PaneErrorBoundary from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/PaneErrorBoundary'
import { useKeyboardNavigation } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useAccessibility'
import { RotateCcw } from 'lucide-react'

type ResizableLayoutProps = {
  leftPanel: React.ReactNode
  rightPanel: React.ReactNode
  aiPane?: React.ReactNode
  minLeftWidth?: number
  minRightWidth?: number
  minAiPaneWidth?: number
  defaultLeftWidth?: number
  showAiPane?: boolean
  dragTreeId?: string // For banner tracking per drag tree
}

export default function ResizableLayout({
  leftPanel,
  rightPanel,
  aiPane,
  minLeftWidth = 20, // percentage - minimum left panel size
  minRightWidth = 20, // percentage - minimum right panel size
  minAiPaneWidth = 15, // percentage - minimum AI pane size
  defaultLeftWidth = 50, // percentage - equal split by default
  showAiPane = false,
  dragTreeId = '',
}: ResizableLayoutProps) {
  const isMobile = useIsMobile()

  // Desktop state
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] =
    useState<boolean>(false)
  const [isAiPaneCollapsed, setIsAiPaneCollapsed] = useState<boolean>(false)

  const containerRef = useRef<HTMLDivElement>(null)

  // ---------------------------------------------
  // Panel flip state (outline ⇄ diagram)
  // ---------------------------------------------
  const LOCAL_KEY = `dragtree_layout_flip_${dragTreeId}`
  const [isFlipped, setIsFlipped] = useState<boolean>(() => {
    if (typeof window === 'undefined') return false
    return window.localStorage.getItem(LOCAL_KEY) === '1'
  })

  const toggleFlip = () => {
    setIsFlipped(prev => {
      const next = !prev
      try {
        window.localStorage.setItem(LOCAL_KEY, next ? '1' : '0')
      } catch {
        // ignore
      }
      return next
    })
  }

  // Refs for collapsible panels
  const rightPanelRef = useRef<ImperativePanelHandle>(null)
  const aiPaneRef = useRef<ImperativePanelHandle>(null)

  // Mobile state
  const [activeTab, setActiveTab] = useState<MobileTab>('tree')

  // Initialize accessibility hooks
  useKeyboardNavigation({
    enableKeyboardNavigation: true,
    enableFocusManagement: true,
    announceNavigationChanges: true,
  })

  // Graceful error recovery function
  const handlePaneRetry = (paneType: 'outline' | 'visualization') => {
    // Instead of page reload, try to reset the specific pane state
    // This provides a better user experience
    try {
      // Force a re-render by updating a key prop or resetting component state
      // For now, we'll use a targeted approach that's less disruptive than page reload
      const event = new CustomEvent('pane-retry', {
        detail: { paneType, timestamp: Date.now() },
      })
      window.dispatchEvent(event)

      // If the custom event approach doesn't work, fall back to page reload
      // but with a small delay to allow for graceful cleanup
      setTimeout(() => {
        if (
          confirm(
            `The ${paneType} pane encountered an error. Reload the page to recover?`
          )
        ) {
          window.location.reload()
        }
      }, 100)
    } catch {
      // Ultimate fallback
      window.location.reload()
    }
  }

  // Handle panel collapse/expand logic for 2-panel or 3-panel layout
  const handlePanelResize = (sizes: number[]) => {
    if (showAiPane && aiPane) {
      // 3-panel layout: left, right, aiPane
      const leftSize = sizes[0]
      const rightSize = sizes[1] || 0
      // const aiPaneSize = sizes[2] || 0 // Currently unused

      // Collapse right panel when left panel exceeds 85% (leaving room for AI pane)
      if (leftSize > 85 && !isRightPanelCollapsed) {
        setIsRightPanelCollapsed(true)
        rightPanelRef.current?.collapse()
      } else if (leftSize <= 85 && isRightPanelCollapsed && rightSize === 0) {
        setIsRightPanelCollapsed(false)
        rightPanelRef.current?.expand()
      }
    } else {
      // 2-panel layout: left, right (original logic)
      const leftSize = sizes[0]
      const rightSize = sizes[1] || 0

      // Collapse right panel when left panel exceeds 90%
      if (leftSize > 90 && !isRightPanelCollapsed) {
        setIsRightPanelCollapsed(true)
        rightPanelRef.current?.collapse()
      } else if (leftSize <= 90 && isRightPanelCollapsed && rightSize === 0) {
        setIsRightPanelCollapsed(false)
        rightPanelRef.current?.expand()
      }
    }
  }

  const handleShowFlowView = () => {
    setIsRightPanelCollapsed(false)
    rightPanelRef.current?.expand()
  }

  const handleShowAiPane = () => {
    setIsAiPaneCollapsed(false)
    aiPaneRef.current?.expand()
  }

  // Calculate default sizes based on whether AI pane is shown
  const getDefaultSizes = () => {
    if (showAiPane && aiPane) {
      // 3-panel layout: adjust sizes to accommodate AI pane
      const aiPaneSize = 25 // AI pane gets 25% by default
      const remaining = 100 - aiPaneSize
      const leftSize = (defaultLeftWidth / 100) * remaining
      const rightSize = remaining - leftSize
      return { leftSize, rightSize, aiPaneSize }
    } else {
      // 2-panel layout: original logic
      return {
        leftSize: defaultLeftWidth,
        rightSize: 100 - defaultLeftWidth,
        aiPaneSize: 0,
      }
    }
  }

  const { leftSize, rightSize, aiPaneSize } = getDefaultSizes()

  // No dynamic positioning needed - button is now fixed at leftmost position

  // Mobile layout with tabs
  if (isMobile) {
    return (
      <div className="h-full max-w-full w-full relative overflow-hidden flex flex-col">
        {/* Mobile Recommendation Banner */}
        {dragTreeId && (
          <MobileRecommendationBanner
            dragTreeId={dragTreeId}
            className="flex-shrink-0"
          />
        )}

        {/* Mobile Tab Navigation */}
        <MobileTabNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
          showAiTab={showAiPane}
          className="flex-shrink-0"
        />

        {/* Mobile Panel Content */}
        <div className="flex-1 overflow-hidden bg-gray-50">
          {/* Tree Panel */}
          <div
            className={cn(
              'h-full overflow-hidden transition-all duration-300',
              {
                block: activeTab === 'tree',
                hidden: activeTab !== 'tree',
              }
            )}
            role="tabpanel"
            id="tree-panel"
            aria-labelledby="tree-tab"
            aria-label="Tree outline view"
            data-pane="outline"
          >
            <div className="h-full p-2">
              <PaneErrorBoundary
                paneType="outline"
                onRetry={() => handlePaneRetry('outline')}
              >
                {leftPanel}
              </PaneErrorBoundary>
            </div>
          </div>

          {/* Diagram Panel */}
          <div
            className={cn(
              'h-full overflow-hidden transition-all duration-300',
              {
                block: activeTab === 'diagram',
                hidden: activeTab !== 'diagram',
              }
            )}
            role="tabpanel"
            id="diagram-panel"
            aria-labelledby="diagram-tab"
            aria-label="Visual flow diagram view"
            data-pane="visualization"
          >
            <div className="h-full p-2">
              <PaneErrorBoundary
                paneType="visualization"
                onRetry={() => handlePaneRetry('visualization')}
              >
                {rightPanel}
              </PaneErrorBoundary>
            </div>
          </div>

          {/* AI Panel */}
          {showAiPane && aiPane && (
            <div
              className={cn(
                'h-full overflow-hidden transition-all duration-300',
                {
                  block: activeTab === 'ai',
                  hidden: activeTab !== 'ai',
                }
              )}
              role="tabpanel"
              id="ai-panel"
              aria-labelledby="ai-tab"
            >
              <div className="h-full">{aiPane}</div>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Desktop layout with resizable panels
  return (
    <div
      ref={containerRef}
      className="h-full max-w-full w-full relative overflow-hidden p-4 pb-2 flex flex-col min-h-0"
    >
      {/* Flip button - positioned at leftmost */}
      <button
        onClick={toggleFlip}
        className="group absolute top-2 left-4 z-40 bg-gray-200/80 hover:bg-blue-400 border border-gray-300/60 hover:border-blue-500 shadow-sm rounded-full p-1 hover:shadow-md transition-all duration-200 ease-out"
        aria-label="Swap panes"
      >
        <RotateCcw className="w-3 h-3 text-gray-600 group-hover:text-white transition-colors duration-200" />
        {/* Tooltip */}
        <span className="absolute top-full left-1/2 -translate-x-1/2 mt-1.5 text-[10px] px-1.5 py-0.5 rounded bg-gray-800 text-white opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200 whitespace-nowrap shadow-lg z-50">
          Swap panes
        </span>
      </button>

      <ResizablePanelGroup
        direction="horizontal"
        onLayout={handlePanelResize}
        className="h-full"
      >
        {/* Left Panel - Tree View (Always visible) */}
        <ResizablePanel
          defaultSize={leftSize}
          minSize={minLeftWidth}
          maxSize={
            showAiPane && aiPane
              ? 100 - minRightWidth - minAiPaneWidth
              : 100 - minRightWidth
          }
          className="overflow-hidden"
        >
          <div
            role="region"
            aria-label="Tree outline view"
            data-pane="outline"
            className={cn(
              'h-full',
              showAiPane ? 'filter blur-sm opacity-60 pointer-events-none' : ''
            )}
          >
            <PaneErrorBoundary
              paneType="outline"
              onRetry={() => handlePaneRetry('outline')}
            >
              {isFlipped ? rightPanel : leftPanel}
            </PaneErrorBoundary>
          </div>
        </ResizablePanel>

        {/* First Resizable Handle - Always rendered */}
        <ResizableHandle
          id="tutorial-resize-handle"
          withHandle
          className={cn(
            'w-1 bg-gray-200 hover:bg-blue-400 transition-colors duration-200 cursor-col-resize',
            'data-[resize-handle-state=hover]:bg-blue-500',
            'data-[resize-handle-state=drag]:bg-blue-500'
          )}
        />

        {/* Right Panel - React Flow (Can be collapsed) */}
        <ResizablePanel
          ref={rightPanelRef}
          defaultSize={rightSize}
          minSize={minRightWidth}
          className="overflow-hidden"
          collapsible={true}
          onCollapse={() => setIsRightPanelCollapsed(true)}
          onExpand={() => setIsRightPanelCollapsed(false)}
        >
          <div
            role="region"
            aria-label="Visual flow diagram view"
            data-pane="visualization"
            className={cn(
              'h-full',
              showAiPane ? 'filter blur-sm opacity-60 pointer-events-none' : ''
            )}
          >
            <PaneErrorBoundary
              paneType="visualization"
              onRetry={() => handlePaneRetry('visualization')}
            >
              {isFlipped ? leftPanel : rightPanel}
            </PaneErrorBoundary>
          </div>
        </ResizablePanel>

        {/* AI Pane Section - Only render if aiPane is provided and showAiPane is true */}
        {showAiPane && aiPane && (
          <>
            {/* Second Resizable Handle - For AI Pane */}
            <ResizableHandle
              id="ai-pane-resize-handle"
              withHandle
              className={cn(
                'w-1 bg-gray-200 hover:bg-blue-400 transition-colors duration-200 cursor-col-resize',
                'data-[resize-handle-state=hover]:bg-blue-500',
                'data-[resize-handle-state=drag]:bg-blue-500'
              )}
            />

            {/* AI Pane - Can be collapsed */}
            <ResizablePanel
              ref={aiPaneRef}
              defaultSize={aiPaneSize}
              minSize={minAiPaneWidth}
              className="overflow-hidden min-w-[20rem]"
              style={{ transition: 'flex-basis 0.5s ease-in-out' }}
              collapsible={true}
              onCollapse={() => setIsAiPaneCollapsed(true)}
              onExpand={() => setIsAiPaneCollapsed(false)}
            >
              <div id="tutorial-ai-pane" className="h-full">
                {aiPane}
              </div>
            </ResizablePanel>
          </>
        )}
      </ResizablePanelGroup>

      {/* Reset Buttons */}
      {isRightPanelCollapsed && (
        <button
          onClick={handleShowFlowView}
          className="
            absolute top-4 right-4 z-10 px-3 py-1 bg-blue-500 text-white text-sm
            rounded-md hover:bg-blue-600 transition-colors duration-200
          "
        >
          {isFlipped ? 'Show Outline View' : 'Show Flow View'}
        </button>
      )}

      {showAiPane && aiPane && isAiPaneCollapsed && (
        <button
          onClick={handleShowAiPane}
          className="
            absolute top-12 right-4 z-10 px-3 py-1 bg-blue-500 text-white text-sm
            rounded-md hover:bg-blue-600 transition-colors duration-200
          "
        >
          Show AI Assistant
        </button>
      )}
    </div>
  )
}
